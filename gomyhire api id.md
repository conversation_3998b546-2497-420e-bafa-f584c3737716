获取系统数据
获取后台用户 获取子分类 获取车辆类型 获取所有数据

                

                    
后台用户

                    
[
  {
    "id": 1,
    "name": "Super Admin",
    "phone": "0162234711",
    "role": "Super Admin"
  },
  {
    "id": 105,
    "name": "<PERSON>wan",
    "phone": null,
    "role": "Super Admin"
  },
  {
    "id": 22,
    "name": "Zahidah1",
    "phone": "0132456789",
    "role": "Operator"
  },
  {
    "id": 106,
    "name": "admin",
    "phone": null,
    "role": "Operator"
  },
  {
    "id": 108,
    "name": "me<PERSON>wan",
    "phone": "0123456789",
    "role": "Operator"
  },
  {
    "id": 143,
    "name": "Kk",
    "phone": "012",
    "role": "Operator"
  },
  {
    "id": 206,
    "name": "OperatorRinglee",
    "phone": "0181122334",
    "role": "Operator"
  },
  {
    "id": 362,
    "name": "<PERSON><PERSON>",
    "phone": null,
    "role": "Operator"
  },
  {
    "id": 110,
    "name": "Sub Admin",
    "phone": "0162234711",
    "role": "Sub_Admin"
  },
  {
    "id": 229,
    "name": "test",
    "phone": null,
    "role": "Sub_Admin"
  },
  {
    "id": 338,
    "name": "jcy1",
    "phone": null,
    "role": "Sub_Admin"
  },
  {
    "id": 163,
    "name": "Kok1",
    "phone": null,
    "role": "Sub_Operator"
  }
]

                

            
                

                    
子分类

                    
[
  {
    "id": 7,
    "main_category": "Airport",
    "name": "Pickup",
    "preset_data": {
      "order_type": "pickup",
      "ota": null,
      "driving_region": "KL - RM - Kuala Lumpur",
      "languages": [
        "JP - Japanese",
        "MY - Malay",
        "CN - Chinese",
        "EN - English",
        "KR - Korean",
        "TML - Tamil"
      ],
      "extra_requirement": "Pickup at the right time please!"
    },
    "required_fields": [
      "Customer Name",
      "Customer Contact",
      "Driving Region",
      "Pickup Address",
      "Pickup Date",
      "Pickup Time",
      "Destination Address",
      "Passenger Number",
      "Driver Fee"
    ]
  },
  {
    "id": 8,
    "main_category": "Airport",
    "name": "Dropoff",
    "preset_data": {
      "order_type": "dropoff",
      "ota": null,
      "driving_region": "KL - RM - Kuala Lumpur",
      "languages": [
        "JP - Japanese",
        "MY - Malay",
        "CN - Chinese",
        "EN - English"
      ],
      "extra_requirement": null
    },
    "required_fields": [
      "Customer Name",
      "Customer Contact",
      "Driving Region",
      "Pickup Address",
      "Pickup Date",
      "Pickup Time",
      "Destination Address",
      "Passenger Number",
      "Driver Fee"
    ]
  },
  {
    "id": 9,
    "main_category": "Chartered",
    "name": "KL to genting",
    "preset_data": {
      "order_type": "charter",
      "ota": null,
      "driving_region": null,
      "languages": [
        "EN - English"
      ],
      "extra_requirement": "asdafdsgghfdfgdjhfygfcvxfgdtgcbncbncghfgfhcvbncvb\r\nncbncbcbcbncb\r\nvcbcvcvn\r\ncvcnbvcbnfhjfhjf\r\nvbncvbncgcfd\r\njhfhvmnvnvbnc\r\nfhfghfhfgjhfg\r\njhbvbnvbnv\r\nnbvnbvbnv\r\nbnvbvbnvbnv\r\nnbvnbvnbvnbv"
    },
    "required_fields": [
      "Customer Name",
      "Customer Contact",
      "Driving Region",
      "Pickup Address",
      "Pickup Date",
      "Pickup Time",
      "Destination Address",
      "Passenger Number",
      "Driver Fee"
    ]
  },
  {
    "id": 10,
    "main_category": "Chartered",
    "name": "KL to melaka",
    "preset_data": {
      "order_type": "charter",
      "ota": null,
      "driving_region": null,
      "languages": [
        "JP - Japanese",
        "MY - Malay",
        "CN - Chinese",
        "EN - English"
      ],
      "extra_requirement": "9am pickup\r\nPutrajaya\r\nStad\r\nJonker street\r\nMasjid Selatan"
    },
    "required_fields": [
      "Customer Name",
      "Customer Contact",
      "Driving Region",
      "Pickup Address",
      "Pickup Date",
      "Pickup Time",
      "Destination Address",
      "Passenger Number",
      "Driver Fee"
    ]
  },
  {
    "id": 11,
    "main_category": "Ticket",
    "name": "Sky mirror",
    "preset_data": {
      "order_type": "charter",
      "ota": "Test",
      "driving_region": "KL - RM - Kuala Lumpur",
      "languages": [
        "CN - Chinese",
        "EN - English"
      ],
      "extra_requirement": null
    },
    "required_fields": [
      "Customer Name",
      "Customer Contact",
      "Pickup Date",
      "Pickup Time",
      "Passenger Number"
    ]
  },
  {
    "id": 12,
    "main_category": "Ticket",
    "name": "Fireflies",
    "preset_data": {
      "order_type": "charter",
      "ota": null,
      "driving_region": "KL - RM - Kuala Lumpur",
      "languages": [
        "JP - Japanese",
        "MY - Malay",
        "CN - Chinese",
        "EN - English"
      ],
      "extra_requirement": "Boarding on 7 pm"
    },
    "required_fields": [
      "Pickup Date",
      "Pickup Time",
      "Passenger Number"
    ]
  },
  {
    "id": 15,
    "main_category": "Airport",
    "name": "携程1",
    "preset_data": {
      "order_type": "dropoff",
      "ota": "Test",
      "driving_region": "SG - SGD - Singapore",
      "languages": [
        "JP - Japanese",
        "MY - Malay",
        "CN - Chinese",
        "EN - English"
      ],
      "extra_requirement": "携程"
    },
    "required_fields": [
      "Customer Name",
      "Customer Contact",
      "Driving Region",
      "Pickup Address",
      "Pickup Date",
      "Pickup Time",
      "Destination Address",
      "Passenger Number",
      "Driver Fee"
    ]
  },
  {
    "id": 16,
    "main_category": "default",
    "name": "default",
    "preset_data": {
      "order_type": null,
      "ota": null,
      "driving_region": null,
      "languages": [],
      "extra_requirement": null
    },
    "required_fields": [
      "Customer Name",
      "Customer Contact",
      "Driving Region",
      "Pickup Address",
      "Pickup Date",
      "Pickup Time",
      "Destination Address",
      "Passenger Number",
      "Driver Fee"
    ]
  },
  {
    "id": 36,
    "main_category": "Charter",
    "name": "Sekinchan",
    "preset_data": {
      "order_type": null,
      "ota": null,
      "driving_region": null,
      "languages": [],
      "extra_requirement": null
    },
    "required_fields": [
      "Customer Name",
      "Customer Contact",
      "Driving Region",
      "Pickup Address",
      "Pickup Date",
      "Pickup Time",
      "Destination Address",
      "Passenger Number",
      "Driver Fee"
    ]
  },
  {
    "id": 41,
    "main_category": "Reward",
    "name": "Reward Sub-Category",
    "preset_data": {
      "order_type": "charter",
      "ota": "Test",
      "driving_region": "KL - RM - Kuala Lumpur",
      "languages": [
        "EN - English",
        "MY - Malay",
        "CN - Chinese"
      ],
      "extra_requirement": "RM100 reward easy"
    },
    "required_fields": [
      "Driver Fee"
    ]
  },
  {
    "id": 43,
    "main_category": "Chartered",
    "name": "Charter",
    "preset_data": {
      "order_type": "charter",
      "ota": null,
      "driving_region": null,
      "languages": [],
      "extra_requirement": null
    },
    "required_fields": [
      "Customer Name",
      "Customer Contact",
      "Driving Region",
      "Pickup Address",
      "Pickup Date",
      "Pickup Time",
      "Destination Address",
      "Passenger Number",
      "Driver Fee"
    ]
  }
]

                

            
                

                    
车辆类型

                    
[
  {
    "id": 5,
    "type": "Compact 5 Seater",
    "seat_number": 4,
    "priority": 1
  },
  {
    "id": 6,
    "type": "Comfort 5 Seater",
    "seat_number": 4,
    "priority": 2
  },
  {
    "id": 15,
    "type": "Mid Size SUV",
    "seat_number": 7,
    "priority": 3
  },
  {
    "id": 16,
    "type": "Standard Size MPV",
    "seat_number": 6,
    "priority": 4
  },
  {
    "id": 31,
    "type": "Luxury Mpv",
    "seat_number": 6,
    "priority": 5
  },
  {
    "id": 32,
    "type": "Alphard/Velfire",
    "seat_number": 6,
    "priority": 6
  },
  {
    "id": 20,
    "type": "10 Seater MPV / Van",
    "seat_number": 9,
    "priority": 7
  },
  {
    "id": 30,
    "type": "12 Seater MPV",
    "seat_number": 11,
    "priority": 8
  },
  {
    "id": 23,
    "type": "14 Seater Van",
    "seat_number": 12,
    "priority": 9
  },
  {
    "id": 24,
    "type": "18 Seater Van",
    "seat_number": 16,
    "priority": 10
  },
  {
    "id": 25,
    "type": "30 Seat Mni Bus",
    "seat_number": 30,
    "priority": 11
  },
  {
    "id": 26,
    "type": "44 Seater Bus",
    "seat_number": 44,
    "priority": 12
  },
  {
    "id": 34,
    "type": "Please Refer Live Chat",
    "seat_number": 1,
    "priority": 13
  }
]

