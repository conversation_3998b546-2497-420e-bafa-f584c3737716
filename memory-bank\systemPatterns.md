# 系统架构模式 - systemPatterns.md

*最后更新: 2024-12-19*

## 🏗️ 整体架构模式

### 分层架构模式 (Layered Architecture)

```
┌─────────────────────────────────────┐
│           表现层 (UI Layer)           │ ← index.html, components/
├─────────────────────────────────────┤
│          应用层 (App Layer)          │ ← core/app.js, 业务逻辑
├─────────────────────────────────────┤
│         服务层 (Service Layer)        │ ← services/, API调用
├─────────────────────────────────────┤
│         配置层 (Config Layer)         │ ← core/config.js, 设置管理
└─────────────────────────────────────┘
```

**设计原则**:
- 单向依赖: 上层可以调用下层，下层不依赖上层
- 职责分离: 每层专注特定职责
- 松耦合: 通过接口隔离层间耦合

### 模块化架构模式

```javascript
// 模块定义模式
const ModulePattern = {
    // IIFE封装保证模块独立性
    createModule: (name, dependencies = []) => {
        return (function(deps) {
            'use strict';
            
            // 私有变量和方法
            const privateVar = {};
            const privateMethod = () => {};
            
            // 公开接口
            return {
                // 模块标识
                name: name,
                version: '1.0.0',
                
                // 初始化方法
                init: function() {},
                
                // 销毁方法  
                destroy: function() {}
            };
        })(dependencies);
    }
};
```

## 🔄 数据流模式

### 单向数据流模式 (Unidirectional Data Flow)

```
用户交互 → 应用状态更新 → UI重新渲染 → 用户交互
    ↑                                      ↓
    └──────── 事件系统反馈 ←────────────────┘
```

**实现示例**:
```javascript
class DataFlowManager {
    constructor() {
        this.state = new AppState();
        this.eventBus = new EventBus();
        this.renderer = new UIRenderer();
    }
    
    // 数据更新入口
    updateState(action) {
        const newState = this.state.reduce(action);
        this.renderer.render(newState);
        this.eventBus.emit('stateChanged', newState);
    }
    
    // 用户操作入口
    handleUserAction(action) {
        this.updateState(action);
    }
}
```

### 发布订阅模式 (Pub/Sub Pattern)

```javascript
class EventBus {
    constructor() {
        this.events = {};
    }
    
    // 订阅事件
    on(event, callback) {
        if (!this.events[event]) {
            this.events[event] = [];
        }
        this.events[event].push(callback);
    }
    
    // 发布事件
    emit(event, data) {
        if (this.events[event]) {
            this.events[event].forEach(callback => callback(data));
        }
    }
    
    // 取消订阅
    off(event, callback) {
        if (this.events[event]) {
            this.events[event] = this.events[event].filter(cb => cb !== callback);
        }
    }
}

// 使用示例
const eventBus = new EventBus();

// 模块间通信
eventBus.on('orderProcessed', (orderData) => {
    logger.info('订单处理完成', orderData);
    notificationManager.success('处理成功', '订单已成功处理');
});

eventBus.emit('orderProcessed', processedOrder);
```

## 🔧 服务层设计模式

### 策略模式 (Strategy Pattern)

```javascript
// OTA类型处理策略
class OTAProcessingStrategy {
    constructor() {
        this.strategies = {
            'chong-dealer': new ChongDealerStrategy(),
            'universal': new UniversalStrategy(), 
            'auto': new AutoDetectStrategy(),
            'other': new GenericStrategy()
        };
    }
    
    process(text, otaType) {
        const strategy = this.strategies[otaType] || this.strategies.other;
        return strategy.execute(text);
    }
}

// 具体策略实现
class ChongDealerStrategy {
    execute(text) {
        // Chong Dealer特定逻辑
        return this.parseChongDealerFormat(text);
    }
    
    parseChongDealerFormat(text) {
        // 16种关键词模式匹配
        const patterns = CHONG_DEALER_PATTERNS;
        return this.matchPatterns(text, patterns);
    }
}
```

### 工厂模式 (Factory Pattern)

```javascript
// API服务工厂
class APIServiceFactory {
    static createService(type) {
        switch(type) {
            case 'deepseek':
                return new DeepSeekService();
            case 'gemini':
                return new GeminiService();
            case 'google-vision':
                return new GoogleVisionService();
            case 'gomyhire':
                return new GoMyHireService();
            default:
                throw new Error(`不支持的服务类型: ${type}`);
        }
    }
    
    static createWithConfig(type, config) {
        const service = this.createService(type);
        service.configure(config);
        return service;
    }
}

// 使用示例
const deepseekService = APIServiceFactory.createWithConfig('deepseek', {
    apiKey: CONFIG.DEEPSEEK_API_KEY,
    timeout: 30000
});
```

### 适配器模式 (Adapter Pattern)

```javascript
// AI服务响应适配器
class AIResponseAdapter {
    static adaptDeepSeekResponse(response) {
        return {
            content: response.choices[0].message.content,
            model: response.model,
            usage: response.usage,
            timestamp: new Date()
        };
    }
    
    static adaptGeminiResponse(response) {
        return {
            content: response.candidates[0].content.parts[0].text,
            model: response.modelVersion,
            usage: response.usageMetadata,
            timestamp: new Date()
        };
    }
    
    static adaptResponse(response, provider) {
        switch(provider) {
            case 'deepseek':
                return this.adaptDeepSeekResponse(response);
            case 'gemini':
                return this.adaptGeminiResponse(response);
            default:
                return response;
        }
    }
}
```

## 🔄 状态管理模式

### 状态管理器模式

```javascript
class StateManager {
    constructor() {
        this.state = this.getInitialState();
        this.listeners = [];
        this.middleware = [];
    }
    
    getInitialState() {
        return {
            auth: { token: null, user: null },
            orders: { list: [], processing: false },
            ui: { loading: false, errors: [] }
        };
    }
    
    // 状态更新通过reducer函数
    dispatch(action) {
        // 应用中间件
        const processedAction = this.applyMiddleware(action);
        
        // 更新状态
        const newState = this.reduce(this.state, processedAction);
        
        if (newState !== this.state) {
            this.state = newState;
            this.notifyListeners();
        }
    }
    
    // 状态监听
    subscribe(listener) {
        this.listeners.push(listener);
        return () => {
            this.listeners = this.listeners.filter(l => l !== listener);
        };
    }
    
    // 通知监听器
    notifyListeners() {
        this.listeners.forEach(listener => listener(this.state));
    }
    
    // 中间件支持
    use(middleware) {
        this.middleware.push(middleware);
    }
    
    applyMiddleware(action) {
        return this.middleware.reduce((acc, middleware) => {
            return middleware(acc);
        }, action);
    }
}
```

### 命令模式 (Command Pattern)

```javascript
// 订单操作命令
class OrderCommand {
    constructor(orderService) {
        this.orderService = orderService;
        this.history = [];
    }
    
    execute(command) {
        const result = command.execute(this.orderService);
        this.history.push(command);
        return result;
    }
    
    undo() {
        const command = this.history.pop();
        if (command && command.undo) {
            return command.undo(this.orderService);
        }
    }
}

// 具体命令实现
class CreateOrderCommand {
    constructor(orderData) {
        this.orderData = orderData;
        this.createdOrderId = null;
    }
    
    execute(orderService) {
        const result = orderService.createOrder(this.orderData);
        this.createdOrderId = result.id;
        return result;
    }
    
    undo(orderService) {
        if (this.createdOrderId) {
            return orderService.deleteOrder(this.createdOrderId);
        }
    }
}
```

## 🛡️ 错误处理模式

### 错误边界模式 (Error Boundary Pattern)

```javascript
class ErrorBoundary {
    constructor(component, errorHandler) {
        this.component = component;
        this.errorHandler = errorHandler;
        this.hasError = false;
    }
    
    wrapMethod(methodName) {
        const originalMethod = this.component[methodName];
        
        this.component[methodName] = async (...args) => {
            try {
                this.hasError = false;
                return await originalMethod.apply(this.component, args);
            } catch (error) {
                this.hasError = true;
                return this.errorHandler.handle(error, methodName, args);
            }
        };
    }
    
    wrapAllMethods() {
        Object.getOwnPropertyNames(this.component.constructor.prototype)
            .filter(name => name !== 'constructor' && typeof this.component[name] === 'function')
            .forEach(name => this.wrapMethod(name));
    }
}

// 错误处理器
class ErrorHandler {
    handle(error, context, args) {
        // 记录错误
        logger.error('ErrorBoundary', `方法 ${context} 执行失败`, { 
            error: error.message, 
            args,
            stack: error.stack 
        });
        
        // 用户友好提示
        notificationManager.error('操作失败', this.getUserFriendlyMessage(error));
        
        // 错误恢复策略
        return this.getRecoveryValue(context);
    }
    
    getUserFriendlyMessage(error) {
        const errorMessages = {
            'NetworkError': '网络连接失败，请检查网络设置',
            'TimeoutError': '操作超时，请重试',
            'ValidationError': '输入数据格式不正确',
            'AuthError': '身份验证失败，请重新登录'
        };
        
        return errorMessages[error.constructor.name] || '发生未知错误，请联系管理员';
    }
}
```

### 重试模式 (Retry Pattern)

```javascript
class RetryManager {
    constructor(options = {}) {
        this.maxRetries = options.maxRetries || 3;
        this.delay = options.delay || 1000;
        this.backoffFactor = options.backoffFactor || 2;
        this.retryCondition = options.retryCondition || (() => true);
    }
    
    async execute(operation, context = '') {
        let lastError;
        
        for (let attempt = 0; attempt <= this.maxRetries; attempt++) {
            try {
                const result = await operation();
                
                if (attempt > 0) {
                    logger.info('Retry', `${context} 重试成功`, { 
                        attempt, 
                        totalAttempts: attempt + 1 
                    });
                }
                
                return result;
            } catch (error) {
                lastError = error;
                
                // 检查是否应该重试
                if (!this.retryCondition(error) || attempt === this.maxRetries) {
                    break;
                }
                
                // 计算延迟时间
                const delay = this.delay * Math.pow(this.backoffFactor, attempt);
                
                logger.warn('Retry', `${context} 重试中`, { 
                    attempt: attempt + 1,
                    maxRetries: this.maxRetries,
                    delay,
                    error: error.message
                });
                
                // 等待后重试
                await this.sleep(delay);
            }
        }
        
        throw lastError;
    }
    
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}
```

## 🔌 插件架构模式

### 插件管理器模式

```javascript
class PluginManager {
    constructor() {
        this.plugins = new Map();
        this.hooks = new Map();
        this.lifecycleHooks = ['beforeInit', 'afterInit', 'beforeDestroy', 'afterDestroy'];
    }
    
    // 注册插件
    register(plugin) {
        if (!plugin.name || !plugin.version) {
            throw new Error('插件必须包含名称和版本信息');
        }
        
        this.plugins.set(plugin.name, plugin);
        
        // 执行插件安装
        if (plugin.install) {
            plugin.install(this);
        }
        
        logger.info('Plugin', `插件 ${plugin.name} v${plugin.version} 已注册`);
    }
    
    // 注册钩子
    addHook(hookName, callback, priority = 10) {
        if (!this.hooks.has(hookName)) {
            this.hooks.set(hookName, []);
        }
        
        this.hooks.get(hookName).push({ callback, priority });
        
        // 按优先级排序
        this.hooks.get(hookName).sort((a, b) => a.priority - b.priority);
    }
    
    // 执行钩子
    async executeHook(hookName, ...args) {
        const hooks = this.hooks.get(hookName) || [];
        let result = args[0];
        
        for (const hook of hooks) {
            try {
                const hookResult = await hook.callback(result, ...args.slice(1));
                if (hookResult !== undefined) {
                    result = hookResult;
                }
            } catch (error) {
                logger.error('Plugin', `钩子 ${hookName} 执行失败`, { error });
            }
        }
        
        return result;
    }
    
    // 获取插件
    getPlugin(name) {
        return this.plugins.get(name);
    }
    
    // 卸载插件
    unregister(name) {
        const plugin = this.plugins.get(name);
        if (plugin) {
            if (plugin.uninstall) {
                plugin.uninstall(this);
            }
            this.plugins.delete(name);
            logger.info('Plugin', `插件 ${name} 已卸载`);
        }
    }
}
```

## 📊 观察者模式

### 日志观察者模式

```javascript
class LogObserver {
    constructor() {
        this.observers = [];
    }
    
    // 添加观察者
    addObserver(observer) {
        this.observers.push(observer);
    }
    
    // 移除观察者
    removeObserver(observer) {
        this.observers = this.observers.filter(obs => obs !== observer);
    }
    
    // 通知所有观察者
    notify(logEntry) {
        this.observers.forEach(observer => {
            try {
                observer.update(logEntry);
            } catch (error) {
                console.error('Observer notification failed:', error);
            }
        });
    }
}

// 具体观察者实现
class ConsoleLogObserver {
    update(logEntry) {
        console.log(`[${logEntry.level}] ${logEntry.module}: ${logEntry.message}`);
    }
}

class NotificationObserver {
    update(logEntry) {
        if (logEntry.level === 'ERROR') {
            notificationManager.error('系统错误', logEntry.message);
        }
    }
}

class MetricsObserver {
    constructor() {
        this.metrics = new Map();
    }
    
    update(logEntry) {
        const key = `${logEntry.level}_${logEntry.module}`;
        const count = this.metrics.get(key) || 0;
        this.metrics.set(key, count + 1);
    }
}
```

## 🎯 依赖注入模式

### 简单依赖注入容器

```javascript
class DIContainer {
    constructor() {
        this.dependencies = new Map();
        this.instances = new Map();
    }
    
    // 注册依赖
    register(name, factory, options = {}) {
        this.dependencies.set(name, {
            factory,
            singleton: options.singleton || false,
            dependencies: options.dependencies || []
        });
    }
    
    // 解析依赖
    resolve(name) {
        const dependency = this.dependencies.get(name);
        
        if (!dependency) {
            throw new Error(`依赖 ${name} 未注册`);
        }
        
        // 单例模式检查
        if (dependency.singleton && this.instances.has(name)) {
            return this.instances.get(name);
        }
        
        // 解析依赖的依赖
        const resolvedDeps = dependency.dependencies.map(dep => this.resolve(dep));
        
        // 创建实例
        const instance = dependency.factory(...resolvedDeps);
        
        // 缓存单例
        if (dependency.singleton) {
            this.instances.set(name, instance);
        }
        
        return instance;
    }
}

// 使用示例
const container = new DIContainer();

// 注册依赖
container.register('config', () => new ConfigManager(), { singleton: true });
container.register('logger', (config) => new Logger(config), { 
    singleton: true, 
    dependencies: ['config'] 
});
container.register('apiService', (config, logger) => new ApiService(config, logger), {
    dependencies: ['config', 'logger']
});

// 解析依赖
const apiService = container.resolve('apiService');
```

---

## 📋 架构模式应用总结

### 当前项目中的模式应用

1. **分层架构**: ✅ 清晰的UI、应用、服务、配置层分离
2. **模块化设计**: ✅ 基于ES6模块和IIFE封装  
3. **策略模式**: ✅ OTA类型处理策略
4. **工厂模式**: ✅ API服务创建工厂
5. **发布订阅**: ✅ 事件总线实现
6. **状态管理**: ✅ 集中式应用状态管理
7. **错误边界**: ✅ 统一错误处理机制
8. **重试模式**: ✅ API调用重试机制

### 待实现的模式

1. **插件架构**: 🔄 为扩展性做准备
2. **依赖注入**: 🔄 更好的依赖管理
3. **命令模式**: 🔄 操作历史和撤销功能
4. **观察者模式**: 🔄 更丰富的事件监听

---

*架构模式会随着项目发展持续演进和优化* 