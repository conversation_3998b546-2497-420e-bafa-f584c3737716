<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LLM智能订单号识别测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-case {
            border: 1px solid #ddd;
            margin: 10px 0;
            padding: 15px;
            border-radius: 4px;
            background-color: #f9f9f9;
        }
        .test-input {
            background-color: #f0f0f0;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            border-left: 4px solid #007bff;
            background-color: #f8f9fa;
        }
        .success {
            border-left-color: #28a745;
            background-color: #d4edda;
        }
        .error {
            border-left-color: #dc3545;
            background-color: #f8d7da;
        }
        .warning {
            border-left-color: #ffc107;
            background-color: #fff3cd;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .code-block {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
        }
        .stats {
            display: flex;
            gap: 20px;
            margin: 20px 0;
        }
        .stat-item {
            background: #e9ecef;
            padding: 10px 15px;
            border-radius: 4px;
            text-align: center;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            font-size: 12px;
            color: #666;
        }
    </style>
</head>
<body>
    <h1>🤖 LLM智能订单号识别测试</h1>
    
    <div class="test-section">
        <h2>📋 功能说明</h2>
        <p>本测试页面用于验证基于LLM的智能订单号识别和提取功能。系统将从订单文本中智能识别原始平台的订单标识符，支持多种格式和排除规则。</p>
        
        <h3>🎯 识别规则</h3>
        <ul>
            <li><strong>纯数字组合</strong>：8-16位连续数字</li>
            <li><strong>字母+数字组合</strong>：2-4个英文字母 + 4-8位数字</li>
            <li><strong>特殊格式组合</strong>：包含特定前缀的组合（如："25kk" + 8位数字）</li>
        </ul>
        
        <h3>🚫 排除规则</h3>
        <ul>
            <li>地址信息、人名、地名、酒店名称</li>
            <li>航班号、车牌号、电话号码、邮箱地址</li>
            <li>与订单处理相关的业务词汇</li>
            <li>时间、日期、价格相关数字</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>🧪 测试用例</h2>
        <button onclick="runAllTests()">运行所有测试</button>
        <button onclick="clearResults()">清空结果</button>
        
        <div class="stats" id="testStats" style="display: none;">
            <div class="stat-item">
                <div class="stat-value" id="totalTests">0</div>
                <div class="stat-label">总测试数</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="passedTests">0</div>
                <div class="stat-label">通过测试</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="failedTests">0</div>
                <div class="stat-label">失败测试</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="successRate">0%</div>
                <div class="stat-label">成功率</div>
            </div>
        </div>
        
        <div id="testResults"></div>
    </div>

    <div class="test-section">
        <h2>🔧 手动测试</h2>
        <p>输入订单文本进行手动测试：</p>
        <textarea id="manualInput" rows="6" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px;" 
                  placeholder="请输入包含订单号的文本..."></textarea>
        <br>
        <button onclick="testManualInput()">测试提取</button>
        <div id="manualResult"></div>
    </div>

    <script>
        // 测试用例数据
        const testCases = [
            {
                name: "明确订单号标识",
                input: "订单号：ABC123456789\n客人姓名：张三\n航班：CZ8301",
                expected: "ABC123456789",
                description: "包含明确订单号标识的文本"
            },
            {
                name: "纯数字订单号",
                input: "预订确认：12345678901234\n酒店：香格里拉大酒店\n日期：2025-01-06",
                expected: "12345678901234",
                description: "14位纯数字订单号"
            },
            {
                name: "特殊格式订单号",
                input: "平台参考：25kk87654321\n服务类型：接机\n乘客：2人",
                expected: "25kk87654321",
                description: "特殊前缀格式订单号"
            },
            {
                name: "字母数字混合",
                input: "确认码：XY9876543Z\n从：吉隆坡机场\n到：市中心酒店",
                expected: "XY9876543Z",
                description: "字母数字混合格式"
            },
            {
                name: "排除航班号",
                input: "航班：CZ8301\n时间：14:30\n联系电话：+60123456789",
                expected: null,
                description: "应该排除航班号，不识别为订单号"
            },
            {
                name: "排除电话号码",
                input: "联系方式：+60123456789\n地址：吉隆坡市中心\n备注：接机服务",
                expected: null,
                description: "应该排除电话号码"
            },
            {
                name: "排除日期时间",
                input: "日期：2025-01-06\n时间：14:30\n价格：RM150",
                expected: null,
                description: "应该排除日期时间格式"
            },
            {
                name: "复杂文本提取",
                input: "预订详情\n订单编号：DEF987654321\n客人：李四\n航班：MH370\n电话：+60987654321\n酒店：双子塔酒店\n日期：2025-01-06 15:00",
                expected: "DEF987654321",
                description: "从复杂文本中正确提取订单号"
            }
        ];

        // 运行所有测试
        async function runAllTests() {
            const resultsDiv = document.getElementById('testResults');
            const statsDiv = document.getElementById('testStats');
            
            resultsDiv.innerHTML = '<p>正在运行测试...</p>';
            statsDiv.style.display = 'flex';
            
            let passed = 0;
            let failed = 0;
            let html = '';
            
            for (let i = 0; i < testCases.length; i++) {
                const testCase = testCases[i];
                html += `<div class="test-case">`;
                html += `<h4>测试 ${i + 1}: ${testCase.name}</h4>`;
                html += `<p>${testCase.description}</p>`;
                html += `<div class="test-input">${testCase.input}</div>`;
                
                try {
                    // 模拟LLM调用（实际应该调用真实的LLM服务）
                    const result = await simulateLLMExtraction(testCase.input);
                    
                    const success = (result.orderNumber === testCase.expected) || 
                                  (result.orderNumber === null && testCase.expected === null);
                    
                    if (success) {
                        passed++;
                        html += `<div class="test-result success">`;
                        html += `✅ 测试通过<br>`;
                    } else {
                        failed++;
                        html += `<div class="test-result error">`;
                        html += `❌ 测试失败<br>`;
                    }
                    
                    html += `<strong>期望结果:</strong> ${testCase.expected || 'null'}<br>`;
                    html += `<strong>实际结果:</strong> ${result.orderNumber || 'null'}<br>`;
                    html += `<strong>置信度:</strong> ${result.confidence}<br>`;
                    html += `<strong>推理:</strong> ${result.reasoning}`;
                    html += `</div>`;
                    
                } catch (error) {
                    failed++;
                    html += `<div class="test-result error">`;
                    html += `❌ 测试异常: ${error.message}`;
                    html += `</div>`;
                }
                
                html += `</div>`;
            }
            
            // 更新统计信息
            const total = passed + failed;
            const successRate = total > 0 ? Math.round((passed / total) * 100) : 0;
            
            document.getElementById('totalTests').textContent = total;
            document.getElementById('passedTests').textContent = passed;
            document.getElementById('failedTests').textContent = failed;
            document.getElementById('successRate').textContent = successRate + '%';
            
            resultsDiv.innerHTML = html;
        }

        // 模拟LLM提取（实际应该调用真实的LLM服务）
        async function simulateLLMExtraction(text) {
            // 模拟网络延迟
            await new Promise(resolve => setTimeout(resolve, 500));
            
            // 简单的模拟逻辑（实际应该调用LLM API）
            const patterns = [
                { regex: /订单号[:：]\s*([A-Z0-9]{8,})/i, confidence: 0.95 },
                { regex: /订单编号[:：]\s*([A-Z0-9]{8,})/i, confidence: 0.95 },
                { regex: /确认码[:：]\s*([A-Z0-9]{8,})/i, confidence: 0.90 },
                { regex: /预订确认[:：]\s*([A-Z0-9]{8,})/i, confidence: 0.90 },
                { regex: /平台参考[:：]\s*([A-Z0-9]{8,})/i, confidence: 0.85 },
                { regex: /(25kk\d{8})/i, confidence: 0.90 },
                { regex: /([A-Z]{2,4}\d{4,8})/g, confidence: 0.70 },
                { regex: /(\d{8,16})/g, confidence: 0.60 }
            ];
            
            // 排除模式
            const excludePatterns = [
                /^\d{4}-\d{2}-\d{2}/, // 日期
                /^\d{2}:\d{2}/, // 时间
                /^[+]?\d{10,15}$/, // 电话
                /^[A-Z]{2}\d{3,4}$/, // 航班号
                /@/ // 邮箱
            ];
            
            for (const { regex, confidence } of patterns) {
                const matches = text.match(regex);
                if (matches) {
                    for (const match of matches) {
                        const candidate = match.length > 1 ? match : matches[1];
                        if (candidate && candidate.length >= 8) {
                            // 检查排除模式
                            let excluded = false;
                            for (const excludePattern of excludePatterns) {
                                if (excludePattern.test(candidate)) {
                                    excluded = true;
                                    break;
                                }
                            }
                            
                            if (!excluded) {
                                return {
                                    orderNumber: candidate,
                                    confidence: confidence,
                                    reasoning: `通过模式 ${regex.toString()} 识别`
                                };
                            }
                        }
                    }
                }
            }
            
            return {
                orderNumber: null,
                confidence: 0.0,
                reasoning: '未找到符合条件的订单号'
            };
        }

        // 手动测试
        async function testManualInput() {
            const input = document.getElementById('manualInput').value.trim();
            const resultDiv = document.getElementById('manualResult');
            
            if (!input) {
                resultDiv.innerHTML = '<div class="test-result error">请输入测试文本</div>';
                return;
            }
            
            resultDiv.innerHTML = '<div class="test-result">正在处理...</div>';
            
            try {
                const result = await simulateLLMExtraction(input);
                
                let html = '<div class="test-result">';
                html += `<strong>提取结果:</strong> ${result.orderNumber || '未找到'}<br>`;
                html += `<strong>置信度:</strong> ${result.confidence}<br>`;
                html += `<strong>推理过程:</strong> ${result.reasoning}`;
                html += '</div>';
                
                resultDiv.innerHTML = html;
                
            } catch (error) {
                resultDiv.innerHTML = `<div class="test-result error">处理失败: ${error.message}</div>`;
            }
        }

        // 清空结果
        function clearResults() {
            document.getElementById('testResults').innerHTML = '';
            document.getElementById('manualResult').innerHTML = '';
            document.getElementById('testStats').style.display = 'none';
        }
    </script>
</body>
</html>
