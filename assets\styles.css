/**
 * @file styles.css - 系统主要样式文件
 * @description 包含系统所有UI组件的样式定义，包括地址搜索功能
 * @updated_at 2024-12-19
 */

/* === 基础样式 === */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.hidden {
    display: none !important;
}

.error-message {
    color: #e74c3c;
    margin-top: 10px;
    font-size: 14px;
}

.success-message {
    color: #27ae60;
    margin-top: 10px;
    font-size: 14px;
}

/* === 模态框样式 === */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    max-width: 400px;
    width: 90%;
}

/* === 表单样式 === */
.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #555;
}

.form-group input, .form-group select, .form-group textarea {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.form-group input:focus, .form-group select:focus, .form-group textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

/* === 按钮样式 === */
button {
    background: #667eea;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
}

button:hover {
    background: #5a6fd8;
    transform: translateY(-2px);
}

.primary-btn {
    background: linear-gradient(45deg, #667eea, #764ba2);
    padding: 12px 24px;
    font-size: 16px;
    font-weight: 500;
}

.primary-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

/* === 主界面布局 === */
#mainApp {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

header {
    background: rgba(255, 255, 255, 0.95);
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

header h1 {
    color: #667eea;
    font-size: 24px;
    font-weight: 600;
}

.header-controls {
    display: flex;
    align-items: center;
    gap: 20px;
}

/* === LLM状态指示器 === */
.llm-status-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: #f8f9fa;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 120px;
}

.llm-status-indicator:hover {
    background: #e9ecef;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.status-light {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #ffc107;
    animation: pulse 2s infinite;
}

.status-light.connected {
    background: #28a745;
    animation: none;
}

.status-light.disconnected {
    background: #dc3545;
    animation: none;
}

.status-light.checking {
    background: #ffc107;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.status-text {
    font-size: 12px;
    color: #495057;
    font-weight: 500;
}

.llm-label {
    background: #667eea;
    color: white;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 9px;
    font-weight: bold;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

/* === 主内容区域 === */
main {
    flex: 1;
    padding: 20px;
    max-width: 1400px;
    margin: 0 auto;
}

.section {
    background: rgba(255, 255, 255, 0.95);
    margin-bottom: 20px;
    padding: 25px;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.section h2 {
    color: #333;
    margin-bottom: 20px;
    font-size: 20px;
    border-bottom: 2px solid #667eea;
    padding-bottom: 10px;
}

/* === 标签页样式 === */
.input-tabs {
    display: flex;
    margin-bottom: 20px;
}

.tab-btn {
    background: #f8f9fa;
    color: #495057;
    border: 1px solid #dee2e6;
    padding: 10px 20px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.tab-btn.active {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

.tab-btn:hover {
    background: #e9ecef;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* === 文本输入区域 === */
#orderText {
    width: 100%;
    height: 200px;
    padding: 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 14px;
    font-family: 'Courier New', monospace;
    resize: vertical;
}

#orderText:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* === 图片上传区域 === */
.upload-area {
    border: 3px dashed #dee2e6;
    border-radius: 10px;
    padding: 40px 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.upload-area:hover {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.05);
}

.upload-area.dragover {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.1);
}

#imageFile {
    display: none;
}

.image-preview {
    margin-top: 20px;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 15px;
}

.image-item {
    position: relative;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.image-item img {
    width: 100%;
    height: 120px;
    object-fit: cover;
}

.image-remove {
    position: absolute;
    top: 5px;
    right: 5px;
    background: rgba(220, 53, 69, 0.9);
    color: white;
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    cursor: pointer;
    font-size: 12px;
}

/* === OTA选择 === */
.ota-selection {
    margin: 20px 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.ota-selection label {
    font-weight: 500;
    color: #555;
}

#otaSelect {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: white;
    min-width: 150px;
}

/* === 结果控制区域 === */
.result-controls {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

.smart-selection-controls {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin: 20px 0;
    border-left: 4px solid #667eea;
}

.smart-selection-controls h3 {
    margin-bottom: 15px;
    color: #333;
    font-size: 16px;
}

.selection-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.selection-row .form-group {
    margin-bottom: 0;
}

.selection-row label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #555;
}

.selection-row select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    background: white;
    font-size: 14px;
}

.selection-row select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.selection-row select:disabled {
    background: #e9ecef;
    color: #6c757d;
}

/* === 结果内容区域 === */
.result-content {
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
    border: 1px solid #e9ecef;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

/* === 手动编辑区域 === */
.manual-edit-section {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-top: 20px;
    border: 1px solid #e9ecef;
}

.manual-edit-section h3 {
    margin-bottom: 15px;
    color: #333;
    font-size: 18px;
}

.edit-controls {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

.secondary-btn {
    background: #6c757d;
    color: white;
    padding: 8px 16px;
    font-size: 14px;
}

.secondary-btn:hover {
    background: #5a6268;
    transform: translateY(-1px);
}

.order-edit-forms {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.order-edit-form {
    background: white;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #dee2e6;
    position: relative;
}

.form-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e9ecef;
}

.form-header h4 {
    color: #333;
    font-size: 16px;
    margin: 0;
}

.remove-form-btn {
    background: #dc3545;
    color: white;
    border: none;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
}

.remove-form-btn:hover {
    background: #c82333;
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

.form-grid .form-group {
    margin-bottom: 0;
}

.form-grid .form-group.full-width {
    grid-column: 1 / -1;
}

.form-grid label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #555;
    font-size: 14px;
}

.form-grid input,
.form-grid select,
.form-grid textarea {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-grid input:focus,
.form-grid select:focus,
.form-grid textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.form-grid input[required] {
    border-left: 3px solid #ffc107;
}

.form-grid input[required]:valid {
    border-left-color: #28a745;
}

.form-grid textarea {
    resize: vertical;
    min-height: 60px;
}

/* === 无订单消息 === */
.no-orders-message {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
    background: #f8f9fa;
    border-radius: 8px;
    border: 2px dashed #dee2e6;
}

.no-orders-message p {
    margin-bottom: 10px;
    font-size: 16px;
}

.no-orders-message .suggestion {
    font-size: 14px;
    color: #495057;
    font-style: italic;
}

/* === 订单项样式 === */
.order-item {
    background: white;
    padding: 20px;
    margin: 15px 0;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.order-item h3 {
    color: #333;
    margin-bottom: 15px;
    font-size: 18px;
}

.order-field {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.order-field label {
    font-weight: 500;
    color: #555;
    min-width: 120px;
}

.order-field input {
    flex: 1;
    margin-left: 10px;
    padding: 6px 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

/* === 操作按钮区域 === */
.action-buttons {
    display: flex;
    gap: 15px;
    margin-top: 20px;
    justify-content: center;
}

#exportBtn {
    background: #28a745;
    padding: 12px 24px;
}

#exportBtn:hover {
    background: #218838;
}

/* === 状态内容 === */
#statusContent {
    padding: 20px;
}

.status-item {
    padding: 15px;
    margin: 10px 0;
    border-radius: 8px;
    border-left: 4px solid #667eea;
    background: #f8f9fa;
}

.status-item.error {
    border-left-color: #dc3545;
    background: #f8d7da;
}

.status-item.pending {
    border-left-color: #ffc107;
    background: #fff3cd;
}

/* === 加载动画 === */
.loading-spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* === 图片分析结果 === */
.image-analysis-result {
    margin-top: 20px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.image-analysis-result h3 {
    margin-bottom: 15px;
    color: #333;
    font-size: 18px;
}

.analysis-tabs {
    display: flex;
    margin-bottom: 15px;
}

.analysis-tab-btn {
    background: #e9ecef;
    color: #495057;
    border: none;
    padding: 8px 16px;
    cursor: pointer;
    border-radius: 4px 4px 0 0;
    margin-right: 2px;
}

.analysis-tab-btn.active {
    background: white;
    color: #333;
    border-bottom: 2px solid #667eea;
}

.analysis-tab-btn:hover {
    background: #f0f0f0;
}

.analysis-content {
    display: none;
}

.analysis-content.active {
    display: block;
}

#extractedTextContent {
    width: 100%;
    height: 200px;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 10px;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    background: white;
    resize: vertical;
}

.analysis-section {
    margin-bottom: 20px;
}

.analysis-section h4 {
    margin: 0 0 10px 0;
    color: #555;
    font-size: 16px;
}

#labelsList, #objectsList {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.label-tag, .object-tag {
    background: #667eea;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    display: inline-flex;
    align-items: center;
    gap: 5px;
}

.confidence-score {
    background: rgba(255, 255, 255, 0.2);
    padding: 2px 6px;
    border-radius: 8px;
    font-size: 11px;
}

.image-info {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
    color: white;
    padding: 10px 8px 5px;
    font-size: 12px;
}

.file-name {
    display: block;
    font-weight: bold;
    margin-bottom: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.file-size {
    opacity: 0.8;
}

/* === 地址搜索组件样式 === */
.address-search-container {
    position: relative;
    width: 100%;
}

.address-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.address-input {
    width: 100%;
    padding: 8px 35px 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 14px;
    color: #495057;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    background: white;
}

.address-input:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.address-input.loading {
    background-image: url('data:image/svg+xml;charset=UTF-8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"/><path d="m9 12 2 2 4-4"/></svg>');
    background-repeat: no-repeat;
    background-position: right 8px center;
    background-size: 16px;
}

.address-search-icon {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    width: 16px;
    height: 16px;
    color: #6c757d;
    pointer-events: none;
}

.address-search-icon.loading {
    animation: spin 1s linear infinite;
}

/* 搜索建议下拉列表 */
.address-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #ced4da;
    border-top: none;
    border-radius: 0 0 4px 4px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    max-height: 200px;
    overflow-y: auto;
    display: none;
}

.address-suggestions.show {
    display: block;
}

.address-suggestion-item {
    padding: 10px 12px;
    cursor: pointer;
    border-bottom: 1px solid #f1f3f4;
    transition: background-color 0.15s ease;
}

.address-suggestion-item:last-child {
    border-bottom: none;
}

.address-suggestion-item:hover,
.address-suggestion-item.highlighted {
    background-color: #f8f9fa;
}

.address-suggestion-item.active {
    background-color: #e3f2fd;
}

.suggestion-main-text {
    font-weight: 500;
    color: #212529;
    margin-bottom: 2px;
    font-size: 14px;
}

.suggestion-secondary-text {
    color: #6c757d;
    font-size: 12px;
    line-height: 1.3;
}

.suggestion-no-results {
    padding: 10px 12px;
    color: #6c757d;
    font-style: italic;
    text-align: center;
    font-size: 13px;
}

.suggestion-error {
    padding: 10px 12px;
    color: #dc3545;
    font-size: 13px;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}

/* 坐标显示区域 */
.coordinates-display {
    margin-top: 5px;
    padding: 5px 8px;
    background-color: #f8f9fa;
    border-radius: 3px;
    font-size: 11px;
    color: #6c757d;
    border-left: 3px solid #28a745;
    display: none;
}

.coordinates-display.show {
    display: block;
}

.coordinates-text {
    font-family: 'Courier New', monospace;
}

.coordinates-success {
    border-left-color: #28a745;
    background-color: #d4edda;
    color: #155724;
}

.coordinates-error {
    border-left-color: #dc3545;
    background-color: #f8d7da;
    color: #721c24;
}

/* 地址验证状态指示器 */
.address-status-indicator {
    position: absolute;
    right: 30px;
    top: 50%;
    transform: translateY(-50%);
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: none;
}

.address-status-indicator.show {
    display: block;
}

.address-status-indicator.success {
    background-color: #28a745;
    box-shadow: 0 0 4px rgba(40, 167, 69, 0.4);
}

.address-status-indicator.error {
    background-color: #dc3545;
    box-shadow: 0 0 4px rgba(220, 53, 69, 0.4);
}

.address-status-indicator.loading {
    background-color: #ffc107;
    box-shadow: 0 0 4px rgba(255, 193, 7, 0.4);
    animation: pulse 1.5s infinite;
}

/* 地址搜索帮助提示 */
.address-search-help {
    margin-top: 5px;
    font-size: 11px;
    color: #6c757d;
    font-style: italic;
}

.address-search-help .help-icon {
    margin-right: 3px;
    color: #007bff;
}

/* 清除地址按钮 */
.clear-address-btn {
    position: absolute;
    right: 25px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    font-size: 12px;
    padding: 2px;
    display: none;
    z-index: 1;
}

.clear-address-btn:hover {
    color: #495057;
}

.clear-address-btn.show {
    display: block;
}

/* 地址类型标签 */
.address-type-badge {
    display: inline-block;
    padding: 2px 6px;
    margin-left: 5px;
    background-color: #e9ecef;
    color: #495057;
    border-radius: 10px;
    font-size: 10px;
    font-weight: 500;
}

.address-type-badge.airport {
    background-color: #d1ecf1;
    color: #0c5460;
}

.address-type-badge.hotel {
    background-color: #d4edda;
    color: #155724;
}

.address-type-badge.restaurant {
    background-color: #fff3cd;
    color: #856404;
}

/* === 响应式设计 === */
@media (max-width: 768px) {
    header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .header-controls {
        flex-direction: column;
        gap: 10px;
    }

    .llm-status-indicator {
        align-self: center;
        min-width: 100px;
        width: 100%;
        justify-content: center;
    }

    .status-text {
        font-size: 10px;
    }

    .llm-label {
        font-size: 8px;
        padding: 1px 4px;
    }

    .user-info {
        justify-content: center;
    }
    
    main {
        padding: 20px 15px;
    }
    
    .section {
        padding: 20px;
    }
    
    .action-buttons {
        flex-direction: column;
    }
    
    .result-controls {
        flex-direction: column;
    }

    .selection-row {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .smart-selection-controls {
        padding: 15px;
        margin: 15px 0;
    }

    .order-field {
        flex-direction: column;
        align-items: flex-start;
    }

    .order-field label {
        width: auto;
        margin-bottom: 5px;
    }

    .order-field input {
        margin-left: 0;
        width: 100%;
    }
    
    .container {
        padding: 10px;
    }
    
    .form-grid {
        grid-template-columns: 1fr;
    }
    
    .address-suggestions {
        max-height: 150px;
        font-size: 13px;
    }
    
    .address-suggestion-item {
        padding: 8px 10px;
    }
    
    .suggestion-main-text {
        font-size: 13px;
    }
    
    .suggestion-secondary-text {
        font-size: 11px;
    }
    
    .coordinates-display {
        font-size: 10px;
    }
    
    .address-search-help {
        font-size: 10px;
    }
}

/* === 滚动条样式 === */
.address-suggestions::-webkit-scrollbar {
    width: 6px;
}

.address-suggestions::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.address-suggestions::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.address-suggestions::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* === 动画效果 === */
.address-suggestions {
    animation: slideDown 0.2s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.address-status-indicator {
    transition: all 0.2s ease;
}

.coordinates-display {
    animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 用户账号数据优化样式 */

/* 创建结果增强样式 */
.results-summary {
    display: flex;
    gap: 12px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.summary-stat {
    padding: 8px 16px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 6px;
}

.summary-stat.success {
    background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
    color: #2e7d32;
    border: 1px solid #a5d6a7;
}

.summary-stat.recovered {
    background: linear-gradient(135deg, #fff3e0, #ffe0b2);
    color: #f57c00;
    border: 1px solid #ffcc02;
}

.summary-stat.failed {
    background: linear-gradient(135deg, #ffebee, #ffcdd2);
    color: #c62828;
    border: 1px solid #ef9a9a;
}

/* 结果项样式增强 */
.create-result {
    margin-bottom: 16px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.create-result:hover {
    box-shadow: 0 4px 16px rgba(0,0,0,0.15);
}

.result-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.result-header h4 {
    margin: 0;
    font-size: 16px;
    color: #495057;
}

.status-badge {
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-badge.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-badge.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.status-badge.recovered {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.status-badge.recoverable {
    background: #cce5ff;
    color: #004085;
    border: 1px solid #99d6ff;
}

.result-details {
    padding: 16px;
}

.result-details p {
    margin: 8px 0;
    line-height: 1.5;
}

.result-details strong {
    color: #495057;
}

.error-message {
    color: #dc3545;
    background: #f8d7da;
    padding: 8px 12px;
    border-radius: 4px;
    border-left: 4px solid #dc3545;
}

.recovery-info {
    color: #856404;
    background: #fff3cd;
    padding: 8px 12px;
    border-radius: 4px;
    border-left: 4px solid #ffc107;
    font-style: italic;
}

/* 错误分析样式 */
.error-analysis {
    margin-top: 12px;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.error-analysis p {
    margin: 6px 0;
    font-size: 14px;
}

.recovery-suggestion {
    margin-top: 8px;
    padding: 8px 12px;
    background: #e7f3ff;
    border-radius: 4px;
    border-left: 4px solid #007bff;
}

.recovery-suggestion p {
    margin: 0;
    color: #004085;
    font-weight: 500;
}

/* 缓存状态样式 */
.cache-status {
    margin-top: 24px;
    padding: 16px;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.cache-status h4 {
    margin: 0 0 12px 0;
    color: #495057;
    font-size: 16px;
}

.cache-status p {
    margin: 6px 0;
    font-size: 14px;
    color: #6c757d;
}

.cache-status .warning {
    color: #856404;
    background: #fff3cd;
    padding: 8px 12px;
    border-radius: 4px;
    border-left: 4px solid #ffc107;
    margin-top: 8px;
}

/* 用户切换确认对话框样式 */
.user-switch-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.user-switch-content {
    background: white;
    padding: 24px;
    border-radius: 12px;
    max-width: 480px;
    width: 90%;
    box-shadow: 0 8px 32px rgba(0,0,0,0.3);
}

.user-switch-content h3 {
    margin: 0 0 16px 0;
    color: #495057;
    text-align: center;
}

.user-switch-content p {
    margin: 12px 0;
    line-height: 1.6;
    color: #6c757d;
}

.user-switch-actions {
    display: flex;
    gap: 12px;
    justify-content: center;
    margin-top: 20px;
}

.user-switch-actions button {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.user-switch-actions .confirm-btn {
    background: #007bff;
    color: white;
}

.user-switch-actions .confirm-btn:hover {
    background: #0056b3;
}

.user-switch-actions .cancel-btn {
    background: #6c757d;
    color: white;
}

.user-switch-actions .cancel-btn:hover {
    background: #545b62;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .results-summary {
        flex-direction: column;
    }
    
    .result-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .user-switch-content {
        margin: 20px;
        width: calc(100% - 40px);
    }
    
    .user-switch-actions {
        flex-direction: column;
    }
} 