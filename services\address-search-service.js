/**
 * @file address-search-service.js - 地址搜索服务
 * @description 集成Google Maps Places API，提供地址搜索、自动完成和坐标获取功能
 * <AUTHOR>
 * @created_at 2024-12-19
 * @updated_at 2024-12-19
 */

/**
 * @class AddressSearchService - 地址搜索服务类
 * @description 处理地址搜索、自动完成、坐标获取等地图相关功能
 */
class AddressSearchService {
    /**
     * @function constructor - 构造函数
     * @description 初始化地址搜索服务
     */
    constructor() {
        this.config = window.SYSTEM_CONFIG?.API?.GOOGLE_MAPS || {};
        this.apiKey = this.config.API_KEY;
        this.isConfigured = this.apiKey && this.apiKey !== 'your-api-key-here';
        
        // 请求缓存
        this.searchResultsCache = new Map();
        this.cacheExpireTime = 5 * 60 * 1000; // 5分钟缓存
        
        // 防抖定时器
        this.debounceTimers = new Map();
        
        // 当前活跃的搜索请求
        this.activeRequests = new Map();
        
        logger.info('AddressSearch', '地址搜索服务初始化', {
            isConfigured: this.isConfigured,
            apiKeyPresent: !!this.apiKey
        });
    }

    /**
     * @function validateConfiguration - 验证配置
     * @returns {boolean} 配置是否有效
     */
    validateConfiguration() {
        if (!this.isConfigured) {
            logger.warn('AddressSearch', 'Google Maps API Key未配置或无效');
            return false;
        }
        return true;
    }

    /**
     * @function generateCacheKey - 生成缓存键
     * @param {string} query - 搜索查询
     * @param {object} options - 搜索选项
     * @returns {string} 缓存键
     */
    generateCacheKey(query, options = {}) {
        const keyData = {
            query: query.toLowerCase().trim(),
            location: options.location,
            types: options.types
        };
        return btoa(JSON.stringify(keyData));
    }

    /**
     * @function getCachedResult - 获取缓存结果
     * @param {string} cacheKey - 缓存键
     * @returns {object|null} 缓存的结果
     */
    getCachedResult(cacheKey) {
        const cached = this.searchResultsCache.get(cacheKey);
        if (cached && Date.now() - cached.timestamp < this.cacheExpireTime) {
            return cached.data;
        }
        
        if (cached) {
            this.searchResultsCache.delete(cacheKey);
        }
        return null;
    }

    /**
     * @function setCachedResult - 设置缓存结果
     * @param {string} cacheKey - 缓存键
     * @param {object} data - 要缓存的数据
     */
    setCachedResult(cacheKey, data) {
        this.searchResultsCache.set(cacheKey, {
            data,
            timestamp: Date.now()
        });
        
        // 限制缓存大小
        if (this.searchResultsCache.size > 100) {
            const firstKey = this.searchResultsCache.keys().next().value;
            this.searchResultsCache.delete(firstKey);
        }
    }

    /**
     * @function searchAddressesWithAutocomplete - 地址自动完成搜索
     * @param {string} query - 搜索查询字符串
     * @param {object} options - 搜索选项
     * @returns {Promise<object>} 搜索结果
     */
    async searchAddressesWithAutocomplete(query, options = {}) {
        if (!this.validateConfiguration()) {
            return {
                success: false,
                error: 'Google Maps API配置无效',
                suggestions: []
            };
        }

        if (!query || query.trim().length < 2) {
            return {
                success: true,
                suggestions: [],
                message: '请输入至少2个字符'
            };
        }

        const trimmedQuery = query.trim();
        const cacheKey = this.generateCacheKey(trimmedQuery, options);
        
        // 检查缓存
        const cached = this.getCachedResult(cacheKey);
        if (cached) {
            logger.debug('AddressSearch', '使用缓存的搜索结果', { query: trimmedQuery });
            return cached;
        }

        try {
            // 取消之前的请求
            if (this.activeRequests.has('autocomplete')) {
                this.activeRequests.get('autocomplete').abort();
            }

            const controller = new AbortController();
            this.activeRequests.set('autocomplete', controller);

            const searchParams = new URLSearchParams({
                input: trimmedQuery,
                key: this.apiKey,
                language: this.config.SEARCH_CONFIG?.language || 'zh-CN',
                components: this.config.SEARCH_CONFIG?.components || 'country:my',
                types: options.types || this.config.SEARCH_CONFIG?.types || 'establishment'
            });

            logger.info('AddressSearch', '开始地址搜索', { 
                query: trimmedQuery,
                url: `${this.config.AUTOCOMPLETE_URL}?${searchParams.toString()}`
            });

            const response = await fetch(
                `${this.config.AUTOCOMPLETE_URL}?${searchParams.toString()}`,
                {
                    method: 'GET',
                    signal: controller.signal,
                    headers: {
                        'Content-Type': 'application/json'
                    }
                }
            );

            this.activeRequests.delete('autocomplete');

            if (!response.ok) {
                throw new Error(`Google Places API请求失败: ${response.status} ${response.statusText}`);
            }

            const data = await response.json();

            if (data.status !== 'OK' && data.status !== 'ZERO_RESULTS') {
                throw new Error(`Google Places API错误: ${data.status} - ${data.error_message || '未知错误'}`);
            }

            const suggestions = await this.formatSearchResults(data.predictions || []);
            
            const result = {
                success: true,
                suggestions,
                query: trimmedQuery,
                count: suggestions.length
            };

            // 缓存结果
            this.setCachedResult(cacheKey, result);

            logger.success('AddressSearch', '地址搜索完成', {
                query: trimmedQuery,
                resultCount: suggestions.length
            });

            return result;

        } catch (error) {
            this.activeRequests.delete('autocomplete');
            
            if (error.name === 'AbortError') {
                logger.debug('AddressSearch', '搜索请求被取消', { query: trimmedQuery });
                return {
                    success: false,
                    error: '搜索被取消',
                    suggestions: []
                };
            }

            logger.error('AddressSearch', '地址搜索失败', error);
            return {
                success: false,
                error: error.message,
                suggestions: []
            };
        }
    }

    /**
     * @function getPlaceDetailsWithCoordinates - 获取地点详情和坐标
     * @param {string} placeId - Google Places的地点ID
     * @returns {Promise<object>} 地点详情和坐标信息
     */
    async getPlaceDetailsWithCoordinates(placeId) {
        if (!this.validateConfiguration()) {
            return {
                success: false,
                error: 'Google Maps API配置无效'
            };
        }

        if (!placeId) {
            return {
                success: false,
                error: '缺少地点ID'
            };
        }

        try {
            const searchParams = new URLSearchParams({
                place_id: placeId,
                key: this.apiKey,
                fields: this.config.PLACE_FIELDS?.join(',') || 'place_id,formatted_address,name,geometry',
                language: this.config.SEARCH_CONFIG?.language || 'zh-CN'
            });

            logger.info('AddressSearch', '获取地点详情', { 
                placeId,
                url: `${this.config.PLACE_DETAILS_URL}?${searchParams.toString()}`
            });

            const response = await fetch(
                `${this.config.PLACE_DETAILS_URL}?${searchParams.toString()}`,
                {
                    method: 'GET',
                    signal: AbortSignal.timeout(this.config.TIMEOUT || 10000),
                    headers: {
                        'Content-Type': 'application/json'
                    }
                }
            );

            if (!response.ok) {
                throw new Error(`Google Places Details API请求失败: ${response.status}`);
            }

            const data = await response.json();

            if (data.status !== 'OK') {
                throw new Error(`Google Places Details API错误: ${data.status} - ${data.error_message || '未知错误'}`);
            }

            const place = data.result;
            const coordinates = this.extractCoordinatesFromPlace(place);

            const result = {
                success: true,
                placeId: place.place_id,
                name: place.name || '',
                formattedAddress: place.formatted_address || '',
                coordinates,
                types: place.types || [],
                place: place
            };

            logger.success('AddressSearch', '地点详情获取成功', {
                placeId,
                name: result.name,
                coordinates
            });

            return result;

        } catch (error) {
            logger.error('AddressSearch', '获取地点详情失败', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * @function geocodeAddress - 地址地理编码
     * @param {string} address - 地址字符串
     * @returns {Promise<object>} 地理编码结果
     */
    async geocodeAddress(address) {
        if (!this.validateConfiguration()) {
            return {
                success: false,
                error: 'Google Maps API配置无效'
            };
        }

        if (!address || !address.trim()) {
            return {
                success: false,
                error: '地址不能为空'
            };
        }

        try {
            const searchParams = new URLSearchParams({
                address: address.trim(),
                key: this.apiKey,
                language: this.config.SEARCH_CONFIG?.language || 'zh-CN',
                components: this.config.SEARCH_CONFIG?.components || 'country:my'
            });

            logger.info('AddressSearch', '开始地址地理编码', { 
                address: address.trim()
            });

            const response = await fetch(
                `${this.config.GEOCODING_API_URL}?${searchParams.toString()}`,
                {
                    method: 'GET',
                    signal: AbortSignal.timeout(this.config.TIMEOUT || 10000),
                    headers: {
                        'Content-Type': 'application/json'
                    }
                }
            );

            if (!response.ok) {
                throw new Error(`Geocoding API请求失败: ${response.status}`);
            }

            const data = await response.json();

            if (data.status !== 'OK' && data.status !== 'ZERO_RESULTS') {
                throw new Error(`Geocoding API错误: ${data.status} - ${data.error_message || '未知错误'}`);
            }

            if (data.results.length === 0) {
                return {
                    success: false,
                    error: '未找到该地址的坐标信息'
                };
            }

            const firstResult = data.results[0];
            const coordinates = this.extractCoordinatesFromPlace(firstResult);

            const result = {
                success: true,
                formattedAddress: firstResult.formatted_address,
                coordinates,
                placeId: firstResult.place_id,
                types: firstResult.types || []
            };

            logger.success('AddressSearch', '地址地理编码完成', {
                address: address.trim(),
                coordinates
            });

            return result;

        } catch (error) {
            logger.error('AddressSearch', '地址地理编码失败', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * @function formatSearchResults - 格式化搜索结果
     * @param {Array} predictions - Google Places API返回的预测结果
     * @returns {Promise<Array>} 格式化后的搜索建议
     */
    async formatSearchResults(predictions) {
        return predictions.slice(0, this.config.MAX_RESULTS || 5).map(prediction => ({
            place_id: prediction.place_id,
            description: prediction.description,
            formatted_address: prediction.description,
            main_text: prediction.structured_formatting?.main_text || prediction.description,
            secondary_text: prediction.structured_formatting?.secondary_text || '',
            place_types: prediction.types || [],
            matched_substrings: prediction.matched_substrings || []
        }));
    }

    /**
     * @function extractCoordinatesFromPlace - 从地点数据中提取坐标
     * @param {object} place - Google Places API返回的地点数据
     * @returns {object} 坐标信息
     */
    extractCoordinatesFromPlace(place) {
        if (!place.geometry || !place.geometry.location) {
            return {
                lat: null,
                lng: null,
                hasCoordinates: false
            };
        }

        return {
            lat: place.geometry.location.lat,
            lng: place.geometry.location.lng,
            hasCoordinates: true,
            viewport: place.geometry.viewport || null
        };
    }

    /**
     * @function searchWithDebounce - 带防抖功能的搜索
     * @param {string} query - 搜索查询
     * @param {function} callback - 回调函数
     * @param {string} debounceKey - 防抖键
     * @param {number} delay - 防抖延迟（毫秒）
     */
    searchWithDebounce(query, callback, debounceKey = 'default', delay = null) {
        const debounceDelay = delay || this.config.DEBOUNCE_DELAY || 300;
        
        // 清除之前的定时器
        if (this.debounceTimers.has(debounceKey)) {
            clearTimeout(this.debounceTimers.get(debounceKey));
        }

        // 设置新的定时器
        const timer = setTimeout(async () => {
            try {
                const result = await this.searchAddressesWithAutocomplete(query);
                callback(result);
            } catch (error) {
                logger.error('AddressSearch', '防抖搜索失败', error);
                callback({
                    success: false,
                    error: error.message,
                    suggestions: []
                });
            }
        }, debounceDelay);

        this.debounceTimers.set(debounceKey, timer);
    }

    /**
     * @function clearCache - 清空搜索缓存
     */
    clearCache() {
        this.searchResultsCache.clear();
        logger.info('AddressSearch', '搜索缓存已清空');
    }

    /**
     * @function cancelAllRequests - 取消所有活跃请求
     */
    cancelAllRequests() {
        for (const [key, controller] of this.activeRequests.entries()) {
            controller.abort();
            this.activeRequests.delete(key);
        }
        logger.info('AddressSearch', '所有活跃搜索请求已取消');
    }

    /**
     * @function getStatus - 获取服务状态
     * @returns {object} 服务状态信息
     */
    getStatus() {
        return {
            isConfigured: this.isConfigured,
            cacheSize: this.searchResultsCache.size,
            activeRequests: this.activeRequests.size,
            lastConfigCheck: Date.now()
        };
    }

    /**
     * @function searchPlaces - 地址搜索的兼容性别名方法
     * @param {string} query - 搜索查询
     * @param {object} options - 搜索选项
     * @returns {Promise<Array>} 搜索建议列表
     */
    async searchPlaces(query, options = {}) {
        const result = await this.searchAddressesWithAutocomplete(query, options);
        return result.suggestions || [];
    }

    /**
     * @function getPlaceDetails - 获取地点详情的兼容性别名方法
     * @param {string} placeId - 地点ID
     * @returns {Promise<object>} 地点详情，包含geometry字段以保持兼容性
     */
    async getPlaceDetails(placeId) {
        const result = await this.getPlaceDetailsWithCoordinates(placeId);
        
        if (result.success && result.coordinates) {
            // 返回与Google Places API格式兼容的结构
            return {
                geometry: {
                    location: {
                        lat: result.coordinates.lat,
                        lng: result.coordinates.lng
                    }
                },
                formatted_address: result.formattedAddress,
                name: result.name,
                place_id: result.placeId,
                types: result.types
            };
        }
        
        return null;
    }
}

// 创建全局实例
window.addressSearchService = window.addressSearchService || new AddressSearchService(); 