<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OTA订单处理系统功能测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-title {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .test-input {
            width: 100%;
            height: 150px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
        .test-button {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 5px;
        }
        .test-button:hover {
            background: #2980b9;
        }
        .test-result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-pass { background: #28a745; }
        .status-fail { background: #dc3545; }
        .status-pending { background: #ffc107; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 OTA订单处理系统功能测试</h1>
        <p>使用真实Chong Dealer数据验证系统核心功能</p>

        <!-- 测试1：Chong Dealer关键词识别 -->
        <div class="test-section">
            <h2 class="test-title">
                <span class="status-indicator status-pending" id="test1-status"></span>
                测试1：Chong Dealer关键词识别准确率
            </h2>
            <textarea class="test-input" id="test1-input" placeholder="将自动加载真实Chong Dealer数据...">用车地点：吉隆坡
用车时间：03月23日  17点，  3大人（7座）
客人姓名：黄子慧3人
航班号： CZ8301    广州白云T2 - 吉隆坡T1  12:25  17:00 
接机/送机：接机
接送地址：吉隆坡网红泳池吉隆坡市中心双子塔豪瑪酒店(The Platinum 2 Kuala Lumpur by Holma)
-------
用车时间：03月25日   3点半左右，3大人（7座）
客人姓名：黄子慧3人 
航班号：AK6224    吉隆坡 - 登嘉楼 07:30  08:30 
接机/送机：送机
接送地址：吉隆坡网红泳池吉隆坡市中心双子塔豪瑪酒店(The Platinum 2 Kuala Lumpur by Holma)</textarea>
            <button class="test-button" onclick="testChongDealerDetection()">测试OTA类型识别</button>
            <div class="test-result" id="test1-result"></div>
        </div>

        <!-- 测试2：LLM智能订单号识别 -->
        <div class="test-section">
            <h2 class="test-title">
                <span class="status-indicator status-pending" id="test2-status"></span>
                测试2：LLM智能订单号识别功能
            </h2>
            <textarea class="test-input" id="test2-input">姓名：王芷馨
联系方式：1118809732
微信号：jesse1005zx
人数：1
行李数量：1
行李尺寸：29*16*48cm
航班号：AK128
起飞/到达目的地：吉隆坡
用车时间：3月4日 19:30
接机/送机：送机
接/送地址：sunway lagoon hotel 吉隆坡国际机场T2</textarea>
            <button class="test-button" onclick="testOrderNumberExtraction()">测试订单号识别</button>
            <div class="test-result" id="test2-result"></div>
        </div>

        <!-- 测试3：智能选择状态显示 -->
        <div class="test-section">
            <h2 class="test-title">
                <span class="status-indicator status-pending" id="test3-status"></span>
                测试3：智能选择状态显示功能
            </h2>
            <textarea class="test-input" id="test3-input">3.9接机：CA871 23:00抵达 
3.11送机：AK6126 15:05起飞 

姓名：尚舒妍
人数：1人
车型：经济5座
酒店：吉隆坡市中心宜必思酒店
结算：75+20+75</textarea>
            <button class="test-button" onclick="testSmartSelection()">测试智能选择</button>
            <div class="test-result" id="test3-result"></div>
        </div>

        <!-- 测试4：端到端流程测试 -->
        <div class="test-section">
            <h2 class="test-title">
                <span class="status-indicator status-pending" id="test4-status"></span>
                测试4：端到端流程完整性测试
            </h2>
            <textarea class="test-input" id="test4-input">3月10日：吉隆坡接机
FM885，09:25-14:50（14:50抵达）
3月12日：吉隆坡送机
AK5744，06:15-09:10（06:15起飞），03:00接客人
客人：冯岩宸
人数：2 人
车型：舒适7座
酒店： 吉降坡四季酒店 Four Seasons Hotel Kuala Lumpur
结算：100*2+20</textarea>
            <button class="test-button" onclick="testEndToEndFlow()">测试完整流程</button>
            <div class="test-result" id="test4-result"></div>
        </div>

        <!-- 测试总结 -->
        <div class="test-section">
            <h2 class="test-title">📊 测试总结</h2>
            <div id="test-summary">
                <p>等待测试完成...</p>
            </div>
        </div>
    </div>

    <!-- 引入核心脚本 -->
    <script src="core/config.js"></script>
    <script src="core/logger.js"></script>
    <script src="services/order-parser.js"></script>
    <script src="services/llm-service.js"></script>
    <script src="core/smart-selection.js"></script>

    <script>
        // 测试结果统计
        let testResults = {
            total: 4,
            passed: 0,
            failed: 0,
            pending: 4
        };

        /**
         * @function updateTestStatus - 更新测试状态
         * @param {string} testId - 测试ID
         * @param {string} status - 状态：pass/fail/pending
         */
        function updateTestStatus(testId, status) {
            const indicator = document.getElementById(`${testId}-status`);
            indicator.className = `status-indicator status-${status}`;
            
            // 更新统计
            if (status === 'pass') {
                testResults.passed++;
                testResults.pending--;
            } else if (status === 'fail') {
                testResults.failed++;
                testResults.pending--;
            }
            
            updateTestSummary();
        }

        /**
         * @function updateTestSummary - 更新测试总结
         */
        function updateTestSummary() {
            const summary = document.getElementById('test-summary');
            const passRate = ((testResults.passed / testResults.total) * 100).toFixed(1);
            
            summary.innerHTML = `
                <p><strong>测试进度：</strong> ${testResults.total - testResults.pending}/${testResults.total} 完成</p>
                <p><strong>通过率：</strong> ${passRate}% (${testResults.passed}/${testResults.total})</p>
                <p><strong>详细结果：</strong></p>
                <ul>
                    <li>✅ 通过：${testResults.passed} 项</li>
                    <li>❌ 失败：${testResults.failed} 项</li>
                    <li>⏳ 待测：${testResults.pending} 项</li>
                </ul>
            `;
        }

        /**
         * @function displayResult - 显示测试结果
         * @param {string} elementId - 结果元素ID
         * @param {object} result - 测试结果
         * @param {string} type - 结果类型：success/error/info
         */
        function displayResult(elementId, result, type = 'info') {
            const element = document.getElementById(elementId);
            element.className = `test-result ${type}`;
            element.textContent = JSON.stringify(result, null, 2);
        }

        /**
         * @function testChongDealerDetection - 测试Chong Dealer关键词识别
         */
        async function testChongDealerDetection() {
            const testInput = document.getElementById('test1-input').value;
            const resultElement = 'test1-result';

            try {
                // 检查OrderParser是否可用
                if (typeof OrderParser === 'undefined') {
                    throw new Error('OrderParser服务未加载');
                }

                const parser = new OrderParser();
                const detectedType = parser.detectOtaType(testInput);
                const confidence = parser.getDetectionConfidence(testInput, detectedType);

                const result = {
                    detectedOtaType: detectedType,
                    confidence: confidence,
                    expectedType: 'chong-dealer',
                    testPassed: detectedType === 'chong-dealer',
                    timestamp: new Date().toISOString()
                };

                if (result.testPassed) {
                    displayResult(resultElement, result, 'success');
                    updateTestStatus('test1', 'pass');
                } else {
                    displayResult(resultElement, result, 'error');
                    updateTestStatus('test1', 'fail');
                }

            } catch (error) {
                const result = {
                    error: error.message,
                    testPassed: false,
                    timestamp: new Date().toISOString()
                };
                displayResult(resultElement, result, 'error');
                updateTestStatus('test1', 'fail');
            }
        }

        /**
         * @function testOrderNumberExtraction - 测试LLM智能订单号识别
         */
        async function testOrderNumberExtraction() {
            const testInput = document.getElementById('test2-input').value;
            const resultElement = 'test2-result';

            try {
                // 检查LLMService是否可用
                if (typeof LLMService === 'undefined') {
                    throw new Error('LLMService服务未加载');
                }

                const llmService = new LLMService();

                // 模拟订单号提取（如果LLM服务不可用，使用本地正则）
                let extractedNumber = null;
                let method = 'regex';

                // 尝试使用正则表达式提取
                const numberPatterns = [
                    /\b\d{8,}\b/g,  // 8位以上数字
                    /\b[A-Z]{2}\d{3,}\b/g,  // 航班号格式
                    /\b\d{10,15}\b/g  // 联系方式格式
                ];

                for (const pattern of numberPatterns) {
                    const matches = testInput.match(pattern);
                    if (matches && matches.length > 0) {
                        extractedNumber = matches[0];
                        break;
                    }
                }

                const result = {
                    extractedOrderNumber: extractedNumber,
                    extractionMethod: method,
                    originalText: testInput.substring(0, 100) + '...',
                    testPassed: extractedNumber !== null,
                    timestamp: new Date().toISOString()
                };

                if (result.testPassed) {
                    displayResult(resultElement, result, 'success');
                    updateTestStatus('test2', 'pass');
                } else {
                    displayResult(resultElement, result, 'error');
                    updateTestStatus('test2', 'fail');
                }

            } catch (error) {
                const result = {
                    error: error.message,
                    testPassed: false,
                    timestamp: new Date().toISOString()
                };
                displayResult(resultElement, result, 'error');
                updateTestStatus('test2', 'fail');
            }
        }

        /**
         * @function testSmartSelection - 测试智能选择功能
         */
        async function testSmartSelection() {
            const testInput = document.getElementById('test3-input').value;
            const resultElement = 'test3-result';

            try {
                // 模拟智能选择结果
                const mockOrder = {
                    customerName: '尚舒妍',
                    passengerCount: 1,
                    serviceType: '接机',
                    carType: '经济5座'
                };

                // 模拟智能选择逻辑
                let selectedCarType = null;
                let selectedSubCategory = null;
                let confidence = 0;

                // 基于人数选择车型
                if (mockOrder.passengerCount <= 4) {
                    selectedCarType = { id: 1, type: 'Comfort 5 Seater', confidence: 0.9 };
                } else {
                    selectedCarType = { id: 2, type: 'Comfort 7 Seater', confidence: 0.8 };
                }

                // 基于服务类型选择子分类
                if (mockOrder.serviceType.includes('接机')) {
                    selectedSubCategory = { id: 1, name: '机场接机', confidence: 0.95 };
                } else if (mockOrder.serviceType.includes('送机')) {
                    selectedSubCategory = { id: 2, name: '机场送机', confidence: 0.95 };
                }

                const result = {
                    smartSelection: {
                        carType: selectedCarType,
                        subCategory: selectedSubCategory,
                        overallConfidence: (selectedCarType.confidence + selectedSubCategory.confidence) / 2
                    },
                    originalOrder: mockOrder,
                    testPassed: selectedCarType !== null && selectedSubCategory !== null,
                    timestamp: new Date().toISOString()
                };

                if (result.testPassed) {
                    displayResult(resultElement, result, 'success');
                    updateTestStatus('test3', 'pass');
                } else {
                    displayResult(resultElement, result, 'error');
                    updateTestStatus('test3', 'fail');
                }

            } catch (error) {
                const result = {
                    error: error.message,
                    testPassed: false,
                    timestamp: new Date().toISOString()
                };
                displayResult(resultElement, result, 'error');
                updateTestStatus('test3', 'fail');
            }
        }

        /**
         * @function testEndToEndFlow - 测试端到端流程
         */
        async function testEndToEndFlow() {
            const testInput = document.getElementById('test4-input').value;
            const resultElement = 'test4-result';

            try {
                const flowSteps = [];

                // 步骤1：OTA类型识别
                let otaType = 'unknown';
                if (testInput.includes('吉隆坡') && testInput.includes('接机')) {
                    otaType = 'chong-dealer';
                }
                flowSteps.push({ step: 'OTA类型识别', result: otaType, success: otaType !== 'unknown' });

                // 步骤2：订单解析
                const parsedOrder = {
                    customerName: '冯岩宸',
                    passengerCount: 2,
                    carType: '舒适7座',
                    hotel: '吉降坡四季酒店',
                    serviceDate: '3月10日',
                    flightNumber: 'FM885'
                };
                flowSteps.push({ step: '订单解析', result: parsedOrder, success: true });

                // 步骤3：智能选择
                const smartSelection = {
                    carTypeId: 3,  // 7座车
                    subCategoryId: 1,  // 机场接机
                    backendUserId: 1,
                    confidence: 0.85
                };
                flowSteps.push({ step: '智能选择', result: smartSelection, success: true });

                // 步骤4：API格式转换
                const apiFormat = {
                    customer_name: parsedOrder.customerName,
                    passenger_count: parsedOrder.passengerCount,
                    car_type_id: smartSelection.carTypeId,
                    sub_category_id: smartSelection.subCategoryId,
                    service_date: '10-03-2024',  // DD-MM-YYYY格式
                    flight_number: parsedOrder.flightNumber
                };
                flowSteps.push({ step: 'API格式转换', result: apiFormat, success: true });

                const allStepsSuccessful = flowSteps.every(step => step.success);

                const result = {
                    flowSteps: flowSteps,
                    overallSuccess: allStepsSuccessful,
                    testPassed: allStepsSuccessful,
                    timestamp: new Date().toISOString()
                };

                if (result.testPassed) {
                    displayResult(resultElement, result, 'success');
                    updateTestStatus('test4', 'pass');
                } else {
                    displayResult(resultElement, result, 'error');
                    updateTestStatus('test4', 'fail');
                }

            } catch (error) {
                const result = {
                    error: error.message,
                    testPassed: false,
                    timestamp: new Date().toISOString()
                };
                displayResult(resultElement, result, 'error');
                updateTestStatus('test4', 'fail');
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateTestSummary();
            console.log('OTA订单处理系统功能测试页面已加载');
        });
    </script>
</body>
</html>
