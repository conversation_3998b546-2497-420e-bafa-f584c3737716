/**
 * 通知弹窗组件
 * 用于显示API响应和其他系统消息
 */
class NotificationManager {
    constructor() {
        this.notifications = [];
        this.container = null;
        this.init();
    }

    /**
     * 初始化通知系统
     */
    init() {
        // 创建通知容器
        this.createContainer();
        
        // 绑定到全局对象
        window.notificationManager = this;
        
        console.log('通知系统初始化完成');
    }

    /**
     * 创建通知容器
     */
    createContainer() {
        // 检查是否已存在容器
        if (document.getElementById('notificationContainer')) {
            this.container = document.getElementById('notificationContainer');
            return;
        }

        // 创建容器元素
        this.container = document.createElement('div');
        this.container.id = 'notificationContainer';
        this.container.className = 'notification-container';
        
        // 添加到页面
        document.body.appendChild(this.container);
    }

    /**
     * 显示通知
     * @param {string} type - 通知类型 (success, error, info, warning)
     * @param {string} title - 通知标题
     * @param {string|object} message - 通知内容
     * @param {number} duration - 显示时长(毫秒)，0表示不自动关闭
     * @param {object} options - 额外选项
     */
    show(type = 'info', title = '', message = '', duration = 5000, options = {}) {
        const notification = {
            id: Date.now() + Math.random(),
            type,
            title,
            message,
            duration,
            timestamp: new Date(),
            ...options
        };

        this.notifications.push(notification);
        this.renderNotification(notification);

        // 自动关闭
        if (duration > 0) {
            setTimeout(() => {
                this.hide(notification.id);
            }, duration);
        }

        return notification.id;
    }

    /**
     * 显示API响应通知
     * @param {object} response - API响应对象
     * @param {string} operation - 操作名称
     */
    showApiResponse(response, operation = '操作') {
        let type, title, message;
        
        if (response.status === true || response.success === true) {
            type = 'success';
            title = `${operation}成功`;
            message = response.message || '操作完成';
            
            // 如果有数据，显示关键信息
            if (response.data && typeof response.data === 'object') {
                if (response.data.order_id) {
                    message += `\n订单ID: ${response.data.order_id}`;
                }
            }
        } else {
            type = 'error';
            title = `${operation}失败`;
            message = response.message || '操作失败';
        }

        // 显示完整响应的选项
        const options = {
            showDetails: true,
            rawResponse: response
        };

        return this.show(type, title, message, 8000, options);
    }

    /**
     * 显示订单创建结果通知
     * @param {array} results - 创建结果数组
     */
    showOrderResults(results) {
        const successCount = results.filter(r => r.success).length;
        const totalCount = results.length;
        
        let type, title, message;
        
        if (successCount === totalCount) {
            type = 'success';
            title = '订单创建成功';
            message = `成功创建 ${successCount} 个订单`;
        } else if (successCount > 0) {
            type = 'warning';
            title = '订单创建部分成功';
            message = `成功创建 ${successCount}/${totalCount} 个订单`;
        } else {
            type = 'error';
            title = '订单创建失败';
            message = `所有订单创建失败`;
        }

        // 显示详细结果
        const options = {
            showDetails: true,
            detailsData: results
        };

        return this.show(type, title, message, 10000, options);
    }

    /**
     * 渲染通知元素
     * @param {object} notification - 通知对象
     */
    renderNotification(notification) {
        const element = document.createElement('div');
        element.className = `notification notification-${notification.type}`;
        element.setAttribute('data-id', notification.id);

        // 图标映射
        const icons = {
            success: '✅',
            error: '❌',
            warning: '⚠️',
            info: 'ℹ️'
        };

        let html = `
            <div class="notification-header">
                <span class="notification-icon">${icons[notification.type] || icons.info}</span>
                <span class="notification-title">${notification.title}</span>
                <button class="notification-close" onclick="notificationManager.hide(${notification.id})">&times;</button>
            </div>
            <div class="notification-body">
                <div class="notification-message">${this.formatMessage(notification.message)}</div>
        `;

        // 添加详情按钮
        if (notification.showDetails) {
            html += `
                <button class="notification-details-btn" onclick="notificationManager.showDetails(${notification.id})">
                    查看详情
                </button>
            `;
        }

        html += `
            </div>
            <div class="notification-time">${this.formatTime(notification.timestamp)}</div>
        `;

        element.innerHTML = html;

        // 添加到容器
        this.container.appendChild(element);

        // 添加动画
        setTimeout(() => {
            element.classList.add('notification-show');
        }, 10);
    }

    /**
     * 格式化消息内容
     * @param {string|object} message - 消息内容
     * @returns {string} 格式化后的HTML
     */
    formatMessage(message) {
        if (typeof message === 'string') {
            return message.replace(/\n/g, '<br>');
        }
        
        if (typeof message === 'object') {
            return `<pre>${JSON.stringify(message, null, 2)}</pre>`;
        }
        
        return String(message);
    }

    /**
     * 格式化时间
     * @param {Date} timestamp - 时间戳
     * @returns {string} 格式化后的时间
     */
    formatTime(timestamp) {
        return timestamp.toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    }

    /**
     * 隐藏通知
     * @param {number} id - 通知ID
     */
    hide(id) {
        const element = this.container.querySelector(`[data-id="${id}"]`);
        if (element) {
            element.classList.add('notification-hide');
            setTimeout(() => {
                if (element.parentNode) {
                    element.parentNode.removeChild(element);
                }
            }, 300);
        }

        // 从数组中移除
        this.notifications = this.notifications.filter(n => n.id !== id);
    }

    /**
     * 显示详情弹窗
     * @param {number} id - 通知ID
     */
    showDetails(id) {
        const notification = this.notifications.find(n => n.id === id);
        if (!notification) return;

        let content = '';
        
        if (notification.rawResponse) {
            content = `
                <h3>API响应详情</h3>
                <pre class="response-details">${JSON.stringify(notification.rawResponse, null, 2)}</pre>
            `;
        } else if (notification.detailsData) {
            content = '<h3>订单创建详情</h3>';
            notification.detailsData.forEach((result, index) => {
                content += `
                    <div class="order-result-detail ${result.success ? 'success' : 'error'}">
                        <h4>订单 ${index + 1}</h4>
                        <p><strong>状态:</strong> ${result.success ? '成功' : '失败'}</p>
                `;
                
                if (result.success && result.result) {
                    if (result.result.id) {
                        content += `<p><strong>订单ID:</strong> ${result.result.id}</p>`;
                    }
                    if (result.result.message) {
                        content += `<p><strong>消息:</strong> ${result.result.message}</p>`;
                    }
                } else if (result.error) {
                    content += `<p><strong>错误:</strong> ${result.error}</p>`;
                }
                
                content += '</div>';
            });
        }

        this.showModal('详情信息', content);
    }

    /**
     * 显示模态框
     * @param {string} title - 标题
     * @param {string} content - 内容
     */
    showModal(title, content) {
        // 创建模态框
        const modal = document.createElement('div');
        modal.className = 'notification-modal';
        modal.innerHTML = `
            <div class="notification-modal-content">
                <div class="notification-modal-header">
                    <h2>${title}</h2>
                    <button class="notification-modal-close" onclick="this.parentElement.parentElement.parentElement.remove()">&times;</button>
                </div>
                <div class="notification-modal-body">
                    ${content}
                </div>
                <div class="notification-modal-footer">
                    <button class="btn-secondary" onclick="this.parentElement.parentElement.parentElement.remove()">关闭</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // 点击背景关闭
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.remove();
            }
        });
    }

    /**
     * 清除所有通知
     */
    clearAll() {
        this.notifications.forEach(notification => {
            this.hide(notification.id);
        });
    }

    /**
     * 快捷方法
     */
    success(title, message, duration = 5000) {
        return this.show('success', title, message, duration);
    }

    error(title, message, duration = 8000) {
        return this.show('error', title, message, duration);
    }

    warning(title, message, duration = 6000) {
        return this.show('warning', title, message, duration);
    }

    info(title, message, duration = 5000) {
        return this.show('info', title, message, duration);
    }
}

// 自动初始化
document.addEventListener('DOMContentLoaded', () => {
    new NotificationManager();
});