
[应用] 开始处理订单 {textLength: 116, otaType: 'auto'}
[UI] 显示加载状态 {message: '正在处理订单...'}
[订单解析] 开始解析订单 {textLength: 116, specifiedOtaType: 'auto'}
[订单解析] OTA类型检测完成 {detectedType: 'chong-dealer', confidence: 0.25}
[订单解析] 使用LLM解析订单 {otaType: 'chong-dealer'}
[LLM] 开始处理订单文本 {textLength: 116, otaType: 'chong-dealer'}
[LLM] 开始调用Gemini API null
=== Gemini 调试信息 ===
订单文本: 5月30日 接机: D7333 11:40抵达

联系人：王倩
人数：2
车型：经济五座
酒店：Hilton Garden Inn Kuala Lumpur Jalan Tuanku Abdul Rahman South

Jy
OTA类型: chong-dealer
完整Prompt: 你是一个专业的订单处理助手。请根据以下需求，对"【订单列表】"中的每条订单信息进行处理。

⚠️ **重要说明**：你只需要专注于订单内容的解析和格式化，不需要进行OTA类型识别。

---

【需求说明】

1. **多重日期判断与修正：**

   - 以当前日期（2025-06-05）为锚点，对订单所填写的日期进行多次验算与推断，确保**最终输出的日期**为**最近且合理的未来日期**。
   - 若发现订单日期已过或与当前日期相比不再合理，则应依次顺延，直至找到**最接近原始日期**但仍位于未来的日期。
   - 若订单日期刚好是今天，或尚未到达，且**逻辑上可行**，则可使用原始日期。
   - 在计算日期时，可按照以下优先级多次验证：
     1. 如本月尚有同日并且未来可用，则使用本月该日；
     2. 若本月该日已过或不合理，则顺延到下个月的同一天；
     3. 若遇到跨年或当月无对应天数等特殊情况，则顺延至下一个最合理日期（例如 2 月无 30 号或 31 号时，需顺延到最近可行日期）。
2. **时间计算：**

   - **接机 (pickup)**：
     - 使用航班"到达时间"作为 `订单时间`。
   - **送机 (dropoff)**：
     - 在航班"起飞时间"的基础上**减去 3.5 小时**，将结果作为 `订单时间`。
   - 若原始时间无法直接解析，请在 `other` 字段中注明原始信息，并根据最近逻辑为 `订单时间` 赋合理数值。
3. **酒店名称：**

   - 若酒店名是中文，请查询后替换为正确且标准的**英文名称**；
   - 若已是英文，且与实际查询结果一致，则原样保留；
   - 如有疑问，请参考以下网站：
     - https://hotels.ctrip.com
     - https://booking.com
     - https://google.com
4. **上下车地点逻辑：**

   - **接机 (pickup)**：`pickup = klia`, `drop = <英文酒店名>`
   - **送机 (dropoff)**：`pickup = <英文酒店名>`, `drop = klia`
   - 任何备注或原始时间信息等，统一放入 `other` 字段中。
5. **专用格式要求：**

   - **时间格式**：采用简短格式 HH:MM（如：14:30）
   - **来源渠道**：在末端增加来源渠道标识 "- Chong Dealer"
   - **联系方式**：与专用格式相同，格式为 "+60-XXXXXXXXX"
6. **默认值设置：**

   - **ota_price**：sedan 69, mpv 80（订单时间在 23:00-07:00 则增加 10）
   - **customer_contact**：与专用格式相同
   - **customer_email**：<EMAIL>
   - **driver_fee**：sedan 65, mpv 75（订单时间在 23:00-07:00 则增加 10）
   - **extra_requirement**："⚠️请进入GMH Link Chat查询微信二维码进群，扫码进群 发车子资料照片 登入群跟客人打招呼和说明服务性质 , 接单后1小时内看群里没二维码向GMH客服领 没处理1小时会自动取消订单"
7. **最终结果：**

   - 为避免歧义，请对**每条订单**都进行**多次日期逻辑检查**，若校验通过则输出修正后日期；若无法确定，请在 `other` 中说明原始信息和推断过程。
   - 输出必须为**纯文本**，不可添加多余解释或 markdown 语法。
   - 结果应为2025年
   - **服务类型判断**：只需判断三种基本类型
     - pickup：接机服务（从机场接客人到酒店/目的地）
     - dropoff：送机服务（从酒店/起点送客人到机场）
     - charter：包车服务（非机场相关的包车、一日游、点对点等）
   - **返回以下字段**：
     ```
     日期: YYYY-MM-DD
     时间: HH:MM-CD
     姓名: [客人姓名]
     航班: [航班号]
     pickup: [上车地点]
     drop: [下车地点]
     service_type: [pickup/dropoff/charter三选一]
     passenger_count: [乘客人数，默认1]
     car_type: [车型：sedan/mpv/van，智能判断]
     car_type_id: [车型ID：sedan=1, mpv=2, van=3]
     ota_price: [sedan 69, mpv 80, van 120，23:00-07:00时段+10]
     customer_contact: [联系电话]
     customer_email: <EMAIL>
     driver_fee: [sedan 65, mpv 75, van 100，23:00-07:00时段+10]
     extra_requirement: ⚠️请进入GMH Link Chat查询微信二维码进群，扫码进群 发车子资料照片 登入群跟客人打招呼和说明服务性质 , 接单后1小时内看群里没二维码向GMH客服领 没处理1小时会自动取消订单
     other: [其他信息 - Chong Dealer]
     ```

---

【订单列表】
5月30日 接机: D7333 11:40抵达

联系人：王倩
人数：2
车型：经济五座
酒店：Hilton Garden Inn Kuala Lumpur Jalan Tuanku Abdul Rahman South

Jy

# `  \n   - **送机 (dropoff)**：`pickup = <英文酒店名>`, `drop = klia`  \n   - 任何备注或原始时间信息等，统一放入 `other` 字段中。\n\n5. **专用格式要求：**\n   - **时间格式**：采用简短格式 HH:MM（如：14:30）\n   - **来源渠道**：在末端增加来源渠道标识 \"- Chong Dealer\"\n   - **联系方式**：与专用格式相同，格式为 \"+60-XXXXXXXXX\"\n\n6. **默认值设置：**\n   - **ota_price**：sedan 69, mpv 80（订单时间在 23:00-07:00 则增加 10）\n   - **customer_contact**：与专用格式相同\n   - **customer_email**：<EMAIL>\n   - **driver_fee**：sedan 65, mpv 75（订单时间在 23:00-07:00 则增加 10）\n   - **extra_requirement**：\"⚠️请进入GMH Link Chat查询微信二维码进群，扫码进群 发车子资料照片 登入群跟客人打招呼和说明服务性质 , 接单后1小时内看群里没二维码向GMH客服领 没处理1小时会自动取消订单\"\n\n7. **最终结果：**  \n   - 为避免歧义，请对**每条订单**都进行**多次日期逻辑检查**，若校验通过则输出修正后日期；若无法确定，请在 `other` 中说明原始信息和推断过程。\n   - 输出必须为**纯文本**，不可添加多余解释或 markdown 语法。\n   - 结果应为2025年\n   - **服务类型判断**：只需判断三种基本类型\n     - pickup：接机服务（从机场接客人到酒店/目的地）\n     - dropoff：送机服务（从酒店/起点送客人到机场）\n     - charter：包车服务（非机场相关的包车、一日游、点对点等）\n   - **返回以下字段**：\n     ```\n     日期: YYYY-MM-DD\n     时间: HH:MM-CD\n     姓名: [客人姓名]\n     航班: [航班号]\n     pickup: [上车地点]\n     drop: [下车地点]\n     service_type: [pickup/dropoff/charter三选一]\n     passenger_count: [乘客人数，默认1]\n     car_type: [车型：sedan/mpv/van，智能判断]\n     car_type_id: [车型ID：sedan=1, mpv=2, van=3]\n     ota_price: [sedan 69, mpv 80, van 120，23:00-07:00时段+10]\n     customer_contact: [联系电话]\n     customer_email: <EMAIL>\n     driver_fee: [sedan 65, mpv 75, van 100，23:00-07:00时段+10]\n     extra_requirement: ⚠️请进入GMH Link Chat查询微信二维码进群，扫码进群 发车子资料照片 登入群跟客人打招呼和说明服务性质 , 接单后1小时内看群里没二维码向GMH客服领 没处理1小时会自动取消订单\n     other: [其他信息 - Chong Dealer]\n     ```\n\n------------------------------------------------\n【订单列表】\n5月30日 接机: D7333 11:40抵达 \n\n\n联系人：王倩\n人数：2\n车型：经济五座\n酒店：Hilton Garden Inn Kuala Lumpur Jalan Tuanku Abdul Rahman South\n\nJy\n\n请严格按照以上要求处理订单信息，包含所有必要字段和默认值。"
        " data-marker="=">请严格按照以上要求处理订单信息，包含所有必要字段和默认值。
请求体:
      ]
    }
  ],
  "generationConfig":
}

[Gemini] 发送API请求
=== Gemini 响应信息 ===
原始JSON响应:
        ],
        "role": "model"
      },
      "finishReason": "STOP",
      "avgLogprobs": -0.004573136846595835
    }
  ],
  "usageMetadata":
    ],
    "candidatesTokensDetails": [

    ]
  },
  "modelVersion": "gemini-1.5-flash-latest",
  "responseId": "OnxBaKzbC8z3ld8P_N2LiA8"
}
提取的内容: 日期: 2025-05-30
时间: 11:40
姓名: 王倩
航班: D7333
pickup: klia
drop: Hilton Garden Inn Kuala Lumpur Jalan Tuanku Abdul Rahman South
service_type: pickup
passenger_count: 2
car_type: sedan
car_type_id: 1
ota_price: 69
customer_contact: +60-XXXXXXXXX
customer_email: <EMAIL>
driver_fee: 65
extra_requirement: ⚠️请进入GMH Link Chat查询微信二维码进群，扫码进群 发车子资料照片 登入群跟客人打招呼和说明服务性质 , 接单后1小时内看群里没二维码向GMH客服领 没处理1小时会自动取消订单
other: 5月30日 接机: D7333 11:40抵达 Jy - Chong Dealer
=== 开始解析LLM响应 ===
原始内容长度: 464
原始内容: 日期: 2025-05-30
时间: 11:40
姓名: 王倩
航班: D7333
pickup: klia
drop: Hilton Garden Inn Kuala Lumpur Jalan Tuanku Abdul Rahman South
service_type: pickup
passenger_count: 2
car_type: sedan
car_type_id: 1
ota_price: 69
customer_contact: +60-XXXXXXXXX
customer_email: <EMAIL>
driver_fee: 65
extra_requirement: ⚠️请进入GMH Link Chat查询微信二维码进群，扫码进群 发车子资料照片 登入群跟客人打招呼和说明服务性质 , 接单后1小时内看群里没二维码向GMH客服领 没处理1小时会自动取消订单
other: 5月30日 接机: D7333 11:40抵达 Jy - Chong Dealer
内容类型: string
清理后的内容: 日期: 2025-05-30
时间: 11:40
姓名: 王倩
航班: D7333
pickup: klia
drop: Hilton Garden Inn Kuala Lumpur Jalan Tuanku Abdul Rahman South
service_type: pickup
passenger_count: 2
car_type: sedan
car_type_id: 1
ota_price: 69
customer_contact: +60-XXXXXXXXX
customer_email: <EMAIL>
driver_fee: 65
extra_requirement: ⚠️请进入GMH Link Chat查询微信二维码进群，扫码进群 发车子资料照片 登入群跟客人打招呼和说明服务性质 , 接单后1小时内看群里没二维码向GMH客服领 没处理1小时会自动取消订单
other: 5月30日 接机: D7333 11:40抵达 Jy - Chong Dealer
清理后的内容长度: 463
=== 开始解析结构化文本 ===
原始文本长度: 463
原始文本内容: "日期: 2025-05-30\n时间: 11:40\n姓名: 王倩\n航班: D7333\npickup: klia\ndrop: Hilton Garden Inn Kuala Lumpur Jalan Tuanku Abdul Rahman South\nservice_type: pickup\npassenger_count: 2\ncar_type: sedan\ncar_type_id: 1\nota_price: 69\ncustomer_contact: +60-XXXXXXXXX\ncustomer_email: <EMAIL>\ndriver_fee: 65\nextra_requirement: ⚠️请进入GMH Link Chat查询微信二维码进群，扫码进群 发车子资料照片 登入群跟客人打招呼和说明服务性质 , 接单后1小时内看群里没二维码向GMH客服领 没处理1小时会自动取消订单\nother: 5月30日 接机: D7333 11:40抵达 Jy - Chong Dealer"
分割后的行数: 16
处理第1行:
找到冒号分隔符 ":" 在位置 2
提取键值对:
字段映射成功: 日期 -> date = 2025-05-30
处理第2行:
找到冒号分隔符 ":" 在位置 2
提取键值对:
字段映射成功: 时间 -> time = 11:40
处理第3行:
找到冒号分隔符 ":" 在位置 2
提取键值对:
字段映射成功: 姓名 -> customer_name = 王倩
处理第4行:
找到冒号分隔符 ":" 在位置 2
提取键值对:
字段映射成功: 航班 -> flight_number = D7333
处理第5行:
找到冒号分隔符 ":" 在位置 6
提取键值对:
字段映射成功: pickup -> pickup_location = klia
处理第6行:
找到冒号分隔符 ":" 在位置 4
提取键值对:
字段映射成功: drop -> drop_location = Hilton Garden Inn Kuala Lumpur Jalan Tuanku Abdul Rahman South
处理第7行:
找到冒号分隔符 ":" 在位置 12
提取键值对:
字段映射成功: service_type -> service_type = pickup
处理第8行:
找到冒号分隔符 ":" 在位置 15
提取键值对:
字段映射成功: passenger_count -> passenger_count = 2
处理第9行:
找到冒号分隔符 ":" 在位置 8
提取键值对:
字段映射成功: car_type -> car_type = sedan
处理第10行:
找到冒号分隔符 ":" 在位置 11
提取键值对:
字段映射成功: car_type_id -> car_type_id = 1
处理第11行:
找到冒号分隔符 ":" 在位置 9
提取键值对:
字段映射成功: ota_price -> ota_price = 69
处理第12行:
找到冒号分隔符 ":" 在位置 16
提取键值对:
字段映射成功: customer_contact -> customer_contact = +60-XXXXXXXXX
处理第13行:
找到冒号分隔符 ":" 在位置 14
提取键值对:
字段映射成功: customer_email -> customer_email = <EMAIL>
处理第14行:
找到冒号分隔符 ":" 在位置 10
提取键值对:
字段映射成功: driver_fee -> driver_fee = 65
处理第15行:
找到冒号分隔符 ":" 在位置 17
提取键值对:
字段映射成功: extra_requirement -> extra_requirement = ⚠️请进入GMH Link Chat查询微信二维码进群，扫码进群 发车子资料照片 登入群跟客人打招呼和说明服务性质 , 接单后1小时内看群里没二维码向GMH客服领 没处理1小时会自动取消订单
处理第16行:
找到冒号分隔符 ":" 在位置 5
提取键值对:
字段映射成功: other -> other = 5月30日 接机: D7333 11:40抵达 Jy - Chong Dealer
=== 解析结构化文本完成 ===
解析后的订单数据:
解析出的字段数量: 16
parseStructuredText返回的数据:
orderData是否为对象: true
orderData键数量: 16
开始转换为标准订单格式...
=== 开始转换为标准订单格式 ===
输入的orderData:
orderData类型: object
orderData是否为null: false
orderData键列表: (16) ['date', 'time', 'customer_name', 'flight_number', 'pickup_location', 'drop_location', 'service_type', 'passenger_count', 'car_type', 'car_type_id', 'ota_price', 'customer_contact', 'customer_email', 'driver_fee', 'extra_requirement', 'other']
生成订单ID: ORDER_1749122106856_aag46f2sz
处理日期时间:
组合后的日期时间: 2025-05-30 11:40
开始构建标准订单对象...
=== 标准订单对象构建完成 ===
标准订单对象:
标准订单对象字段数量: 19
关键字段检查:
convertToStandardOrder返回的数据:
成功解析订单:
=== 解析完成 ===
解析出的订单数量: 1
最终返回结果:}
解析结果:
  ],
  "metadata":
}
=

[Gemini] API请求成功 {responseTime: 2922, contentLength: 464}
[LLM] Gemini API调用完成 {processingTime: 2923, success: true, error: null}
[LLM] Gemini处理成功 {totalProcessingTime: 2924, apiCallTime: 2923, provider: 'gemini'}
[SmartSelection] 车型选择完成 {carTypeId: 5, carTypeName: 'Compact 5 Seater', method: 'passenger_count_exact', confidence: 0.95}
[SmartSelection] 服务类型选择完成 {subCategoryId: 7, subCategoryName: 'Pickup', method: 'simplified_mapping', confidence: 0.9}
[SmartSelection] 后台用户选择完成 {userId: 3, userName: 'Operator', method: 'flight_service', confidence: 0.8}
[SmartSelection] 智能选择完成 {source: 'llm', processingTime: 11, successfulSelections: 3, fallbacks: 0}
[订单解析] 智能选择已应用到LLM解析结果 {originalOrder: {…}, enhancedOrder: {…}}
[订单解析] 订单解析完成 {otaType: 'chong-dealer', orderCount: 1, processingTime: '2938ms', success: true}
[UI] 显示订单结果 {orderCount: 1, otaType: 'chong-dealer', showManualEdit: false}
[应用] 订单处理完成 {orderCount: 1}
[UI] 隐藏加载状态 null
[订单] 开始创建订单流程 null
[API] 发起获取后端用户请求 null
[API响应] GET https://staging.gomyhire.com.my/api/backend_users - 200  {type: 'API_RESPONSE', method: 'GET', url: 'https://staging.gomyhire.com.my/api/backend_users', status: 200, timestamp: '2025-06-05T11:15:11.375Z', …}
[AppState] 用户数据缓存已更新 {userHash: 'c3VwZXJfYWRtaW5AZ29teWhpcmUuY29tX2Fub255bW91cw', key: 'backendUsers', dataSize: 756}
[API] 获取后端用户成功 {userCount: 12, userNames: Array(5)}
[DataConsistency] 数据一致性验证完成 {userHash: 'c3VwZXJfYWRtaW5AZ29teWhpcmUuY29tX2Fub255bW91cw', completeness: true, freshness: true, association: true, isValid: true}
[UI] 显示加载状态 {message: '正在创建订单...'}
[订单] 准备创建 1 个订单 null
[订单] 正在处理第 1 个订单 {id: 'ORDER_1749122106856_aag46f2sz', customer_name: '王倩', customer_contact: '+60-XXXXXXXXX', customer_email: '<EMAIL>', service_type: 'pickup', …}
[API] 发起创建订单请求 {hasData: true, dataKeys: Array(23)}
[API请求] POST https://staging.gomyhire.com.my/api/create_order {type: 'API_REQUEST', method: 'POST', url: 'https://staging.gomyhire.com.my/api/create_order', timestamp: '2025-06-05T11:15:11.380Z', headers: {…}, …}
[API响应] POST https://staging.gomyhire.com.my/api/create_order - 200 (131ms) {type: 'API_RESPONSE', method: 'POST', url: 'https://staging.gomyhire.com.my/api/create_order', status: 200, timestamp: '2025-06-05T11:15:11.511Z', …}
[API] 创建订单成功 {responseTime: '131ms', orderId: 'unknown'}
[订单] 第 1 个订单创建成功 {status: false, message: 'Data need to be refined', data: {…}}
[UI] 隐藏加载状态 null
[UI] 显示创建结果 {successCount: 1, recoveredCount: 0, totalCount: 1}

### API响应详情

```
{
  "status": false,
  "message": "Data need to be refined",
  "data": {
    "validation_error": {
      "ota_reference_number": [
        "The ota reference number field is required."
      ]
    },
    "available_fields_to_fill_in": [
      "sub_category_id",
      "ota",
      "ota_reference_number",
      "ota_price",
      "customer_name",
      "customer_contact",
      "customer_email",
      "flight_info",
      "pickup",
      "pickup_lat",
      "pickup_long",
      "date",
      "time",
      "destination",
      "destination_lat",
      "destination_long",
      "car_type_id",
      "passenger_number",
      "luggage_number",
      "driver_fee",
      "driver_collect",
      "tour_guide",
      "baby_chair",
      "meet_and_greet",
      "extra_requirement",
      "incharge_by_backend_user_id"
    ]
  }
}
```
