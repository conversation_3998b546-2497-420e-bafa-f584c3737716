/**
 * @file prompts-config.js - Gemini 提示词配置文件
 * @description 包含所有OTA类型的Gemini AI提示词配置
 */

// 提示词配置对象
const GEMINI_PROMPTS = {
    


    // Chong Dealer 专用提示词
    CHONG_DEALER: {
        name: 'Chong Dealer订单处理',
        description: '专门处理Chong Dealer类型的订单',
        prompt: `你是一个专业的订单处理助手。请根据以下需求，对"【订单列表】"中的每条订单信息进行处理。

⚠️ **重要说明**：你只需要专注于订单内容的解析和格式化，不需要进行OTA类型识别。

------------------------------------------------
【需求说明】

1. **多重日期判断与修正：**  
   - 以当前日期（{current_date}）为锚点，对订单所填写的日期进行多次验算与推断，确保**最终输出的日期**为**最近且合理的未来日期**。  
   - 若发现订单日期已过或与当前日期相比不再合理，则应依次顺延，直至找到**最接近原始日期**但仍位于未来的日期。  
   - 若订单日期刚好是今天，或尚未到达，且**逻辑上可行**，则可使用原始日期。  
   - 在计算日期时，可按照以下优先级多次验证：  
     1. 如本月尚有同日并且未来可用，则使用本月该日；  
     2. 若本月该日已过或不合理，则顺延到下个月的同一天；  
     3. 若遇到跨年或当月无对应天数等特殊情况，则顺延至下一个最合理日期（例如 2 月无 30 号或 31 号时，需顺延到最近可行日期）。  

2. **时间计算：**  
   - **接机 (pickup)**：  
     - 使用航班"到达时间"作为 \`订单时间\`。  
   - **送机 (dropoff)**：  
     - 在航班"起飞时间"的基础上**减去 3.5 小时**，将结果作为 \`订单时间\`。  
   - 若原始时间无法直接解析，请在 \`other\` 字段中注明原始信息，并根据最近逻辑为 \`订单时间\` 赋合理数值。

3. **酒店名称：**  
   - 若酒店名是中文，请查询后替换为正确且标准的**英文名称**；  
   - 若已是英文，且与实际查询结果一致，则原样保留；  
   - 如有疑问，请参考以下网站：  
     - https://hotels.ctrip.com  
     - https://booking.com  
     - https://google.com  

4. **上下车地点逻辑：**  
   - **接机 (pickup)**：\`pickup = klia\`, \`drop = <英文酒店名>\`  
   - **送机 (dropoff)**：\`pickup = <英文酒店名>\`, \`drop = klia\`  
   - 任何备注或原始时间信息等，统一放入 \`other\` 字段中。

5. **专用格式要求：**
   - **时间格式**：采用简短格式 HH:MM（如：14:30）
   - **来源渠道**：在末端增加来源渠道标识 "- Chong Dealer"
   - **联系方式**：与专用格式相同，格式为 "+60-XXXXXXXXX"

6. **默认值设置：**
   - **ota_price**：sedan 69, mpv 80（订单时间在 23:00-07:00 则增加 10）
   - **customer_contact**：与专用格式相同
   - **customer_email**：<EMAIL>
   - **driver_fee**：sedan 65, mpv 75（订单时间在 23:00-07:00 则增加 10）
   - **extra_requirement**："⚠️请进入GMH Link Chat查询微信二维码进群，扫码进群 发车子资料照片 登入群跟客人打招呼和说明服务性质 , 接单后1小时内看群里没二维码向GMH客服领 没处理1小时会自动取消订单"

7. **最终结果：**  
   - 为避免歧义，请对**每条订单**都进行**多次日期逻辑检查**，若校验通过则输出修正后日期；若无法确定，请在 \`other\` 中说明原始信息和推断过程。
   - 输出必须为**纯文本**，不可添加多余解释或 markdown 语法。
   - 结果应为2025年
   - **服务类型判断**：只需判断三种基本类型
     - pickup：接机服务（从机场接客人到酒店/目的地）
     - dropoff：送机服务（从酒店/起点送客人到机场）
     - charter：包车服务（非机场相关的包车、一日游、点对点等）
   - **返回以下字段**：
     \`\`\`
     日期: YYYY-MM-DD
     时间: HH:MM-CD
     姓名: [客人姓名]
     航班: [航班号]
     pickup: [上车地点]
     drop: [下车地点]
     service_type: [pickup/dropoff/charter三选一]
     passenger_count: [乘客人数，默认1]
     car_type: [车型：sedan/mpv/van，智能判断]
     car_type_id: [车型ID：sedan=1, mpv=2, van=3]
     ota_price: [sedan 69, mpv 80, van 120，23:00-07:00时段+10]
     customer_contact: [联系电话]
     customer_email: <EMAIL>
     driver_fee: [sedan 65, mpv 75, van 100，23:00-07:00时段+10]
     extra_requirement: ⚠️请进入GMH Link Chat查询微信二维码进群，扫码进群 发车子资料照片 登入群跟客人打招呼和说明服务性质 , 接单后1小时内看群里没二维码向GMH客服领 没处理1小时会自动取消订单
     other: [其他信息 - Chong Dealer]
     \`\`\`

------------------------------------------------
【订单列表】
{input}

请严格按照以上要求处理订单信息，包含所有必要字段和默认值。`
    },



    // 通用订单处理模板（合并版）
    UNIVERSAL_FALLBACK: {
        name: '通用订单处理模板',
        description: '处理无法通过OTA关键词识别的订单，满足GoMyHire API字段需求',
        prompt: `你是一个专业的通用订单处理助手，专门处理各种格式的订单信息。请按照以下规则处理订单：

【通用处理规则】

1. **智能订单类型识别：**
   - 自动识别服务类型：只需判断三种基本类型（pickup/dropoff/charter）
   - 根据航班信息和时间判断服务性质
   - 识别特殊服务需求（如举牌服务、婴儿座椅等）
   - 服务类型判断规则：
     * pickup：接机服务（从机场接客人到酒店/目的地）
     * dropoff：送机服务（从酒店/起点送客人到机场）
     * charter：包车服务（非机场相关的包车、一日游、点对点等）

2. **完整信息提取：**
   - 客人姓名和联系方式
   - 航班号和航班时间
   - 服务日期和时间
   - 上下车地点
   - 乘客人数
   - 车型需求
   - 特殊要求和备注

3. **日期时间处理：**
   - 当前日期：{current_date}
   - 确保所有日期为未来日期（2025年）
   - 接机：使用航班到达时间
   - 送机：航班起飞时间减去3.5小时
   - 自动修正过期日期到下个月同一天
   - 时间格式：HH:MM（24小时制）

4. **地点标准化：**
   - 中文酒店名转换为英文标准名称
   - 接机：pickup = klia, drop = 酒店英文名
   - 送机：pickup = 酒店英文名, drop = klia
   - 包车/点对点：根据实际地点填写
   - 地址标准化为完整英文地址

5. **联系信息处理：**
   - 提取或生成联系电话（格式：+60-XXXXXXXXX）
   - 生成客户邮箱（如无则使用默认）
   - 生成唯一OTA参考号：YYYYMMDDHHMM-航班号-客人姓名-FALLBACK

6. **车型智能判断和ID映射：**
   - **车型判断优先级**：
     1. 优先识别订单中明确提到的车型关键词（如：经济、商务、豪华、轿车、MPV、七座、十座等）
     2. 根据乘客人数进行智能匹配：1-4人→sedan，5-8人→mpv，9人以上→van
     3. 考虑特殊需求（如行李多、老人小孩、商务接待等）适当升级车型
   - **车型标准化映射**：
     - sedan（轿车/经济型）：适用1-4人，关键词：经济、轿车、四座、五座、compact、economy
     - mpv（商务车/七座）：适用5-8人，关键词：商务、七座、MPV、comfort、business
     - van（大型车/十座以上）：适用9人以上，关键词：十座、大巴、van、luxury、premium
   - **车型ID映射规则**：
     - 输出时必须同时提供车型类型(car_type)和对应的车型ID(car_type_id)
     - sedan → car_type_id: 1
     - mpv → car_type_id: 2  
     - van → car_type_id: 3
   - **价格计算**：
     - sedan基础价69，mpv基础价80，van基础价120
     - 夜间服务（23:00-07:00）各车型加收10
   - **司机费用**：
     - sedan基础费65，mpv基础费75，van基础费100
     - 夜间服务司机费用同样加收10

7. **质量控制与验证：**
   - 验证信息的逻辑性和完整性
   - 标记可能的错误或疑问
   - 提供处理建议和置信度评估
   - 确保所有GoMyHire API必填字段都有值

【GoMyHire API完整字段输出格式】
请严格按照以下格式输出，每个字段一行：

\`\`\`
日期: YYYY-MM-DD
时间: HH:MM
姓名: [客人姓名]
电话: [联系电话，XXXXXXXXX]
邮箱: [客户邮箱，如无则空值]
航班: [航班号]
pickup: [上车地点英文名]
drop: [下车地点英文名]
service_type: [pickup/dropoff/charter三选一]
passenger_count: [乘客人数]
vehicle_type: [车型：sedan/mpv/van]
     vehicle_type_id: [车型ID：sedan=1, mpv=2, van=3]
     ota_price: [OTA价格：sedan 69, mpv 80, van 120，夜间+10]
     driver_fee: [司机费用：sedan 65, mpv 75, van 100，夜间+10]
special_requirements: [特殊要求]
other: [其他信息]
ota_reference_number: [OTA参考号：YYYYMMDDHHMM-航班号-客人姓名-FALLBACK]
confidence: [置信度0-100]
extraction_method: [提取方法：direct/inferred/generated]
processing_notes: [处理说明和质量控制备注]
\`\`\`

每条订单之间用空行分隔。

【订单内容】
{input}

请处理上述订单信息，确保提取完整准确。`
    },



    // 图片OCR识别提示词
    IMAGE_OCR: {
        name: '图片OCR识别',
        description: '用于从图片中提取订单信息',
        prompt: `你是一个专业的图片文字识别和订单信息提取专家。请分析这张图片中的订单信息，并按照以下要求提取和处理：

【图片分析要求】

1. **文字识别：**
   - 识别图片中的所有文字内容
   - 注意中英文混合的情况
   - 识别表格、表单结构
   - 处理手写文字（如果有）

2. **信息提取：**
   - 日期和时间信息
   - 客人姓名和联系方式
   - 航班号和航班时间
   - 酒店名称和地址
   - 服务类型（接机/送机）
   - 特殊要求和备注

3. **数据验证：**
   - 检查日期格式的合理性
   - 验证航班号格式
   - 确认时间的逻辑性
   - 标记可能的OCR错误

【处理规则】

1. **日期处理：**
   - 当前日期：{current_date}
   - 确保识别的日期为未来日期
   - 如果日期已过，建议调整方案

2. **格式标准化：**
   - 日期：YYYY-MM-DD
   - 时间：HH:MM
   - 电话：标准格式

3. **信息补全：**
   - 生成缺失的OTA参考号
   - 标准化地点名称
   - 补充必要的联系信息

【输出格式】
请以以下格式输出识别结果：

\`\`\`
=== OCR识别原始内容 ===
[图片中识别到的原始文字内容]

=== 提取的订单信息 ===
日期: YYYY-MM-DD
时间: HH:MM
姓名: [客人姓名]
电话: [联系电话]
航班: [航班号]
pickup: [上车地点]
drop: [下车地点]
service_type: [接机/送机]
other: [其他信息]
ota_reference_number: [OTA参考号]

=== 处理说明 ===
- OCR置信度: [高/中/低]
- 可能的错误: [列出可能的识别错误]
- 建议检查: [需要人工确认的项目]
- 处理备注: [其他处理说明]
\`\`\`

请分析图片并提取订单信息。`
    },

    // 图片质量评估提示词
    IMAGE_QUALITY: {
        name: '图片质量评估',
        description: '评估图片质量和OCR可行性',
        prompt: `请评估这张图片的质量，并提供OCR识别的可行性分析：

【评估维度】

1. **图片清晰度：**
   - 分辨率是否足够
   - 文字是否清晰可读
   - 是否有模糊或失焦

2. **光线条件：**
   - 亮度是否适中
   - 是否有阴影遮挡
   - 对比度是否足够

3. **文字布局：**
   - 文字排列是否整齐
   - 是否有倾斜或变形
   - 表格结构是否完整

4. **干扰因素：**
   - 是否有背景干扰
   - 是否有污渍或折痕
   - 是否有反光或阴影

【输出格式】
\`\`\`
=== 图片质量评估 ===
整体质量: [优秀/良好/一般/较差]
清晰度: [评分1-10]
可读性: [评分1-10]
OCR成功率预估: [百分比]

=== 具体问题 ===
- [列出发现的问题]
- [提供改善建议]

=== 处理建议 ===
- [是否建议重新拍摄]
- [是否需要图片预处理]
- [OCR识别注意事项]
\`\`\`

请评估图片质量。`
    },

    // 订单号智能识别提示词
    ORDER_NUMBER_EXTRACTION: {
        name: '订单号智能识别',
        description: '从订单文本中智能识别和提取原始平台订单标识符',
        prompt: `你是一个专业的订单号识别专家。请从以下订单文本中识别并提取原始平台的订单标识符。

【识别规则】
请识别符合以下特征的订单标识符：

1. **纯数字组合**：
   - 8-16位连续数字（如：12345678901234）
   - 排除：电话号码、日期、时间

2. **字母+数字组合**：
   - 2-4个英文字母 + 4-8位数字（如：ABC123456）
   - 数字+字母的混合组合，长度8-12位（如：1A2B3C4D5）

3. **特殊格式组合**：
   - 包含特定前缀的组合（如："25kk" + 8位数字）
   - 其他平台特有的标识符格式

【排除规则】
请排除以下内容，不要将其识别为订单号：
- 地址信息（街道名、门牌号）
- 人名、地名、酒店名称
- 航班号、车牌号
- 电话号码、邮箱地址
- 与订单处理相关的业务词汇（接机、送机、包车等）
- 时间、日期相关数字
- 价格、费用相关数字

【最小长度要求】
- 只提取8个字符以上的标识符
- 优先提取明显的订单号格式
- 其次提取符合特征的字母数字组合

【输出格式】
请严格按照以下JSON格式输出，不要添加任何其他内容：

\`\`\`json
{
  "orderNumber": "提取的订单号或null",
  "confidence": "置信度(0.0-1.0)",
  "reasoning": "识别原因说明"
}
\`\`\`

【订单文本】
{input}

请分析上述订单文本，识别并提取原始平台的订单标识符。`
    }
};

// 提示词工具类
class PromptManager {
    constructor() {
        this.prompts = GEMINI_PROMPTS;
    }

    /**
     * 获取指定类型的提示词
     * @param {string} type - 提示词类型
     * @param {Object} params - 参数对象
     * @returns {string} 处理后的提示词
     */
    getPrompt(type, params = {}) {
        const promptConfig = this.prompts[type];
        if (!promptConfig) {
            throw new Error(`未找到提示词类型: ${type}`);
        }

        let prompt = promptConfig.prompt;
        
        // 替换参数
        Object.keys(params).forEach(key => {
            const placeholder = `{${key}}`;
            prompt = prompt.replace(new RegExp(placeholder, 'g'), params[key]);
        });

        return prompt;
    }

    /**
     * 根据OTA类型获取对应的处理提示词
     * @param {string} otaType - OTA类型
     * @param {string} input - 输入内容
     * @param {string} currentDate - 当前日期
     * @returns {string} 处理后的提示词
     */
    getOTAPrompt(otaType, input, currentDate) {
        const params = {
            input: input,
            current_date: currentDate
        };

        switch (otaType.toLowerCase()) {
            case 'chong-dealer':
            case 'chong_dealer':
            case 'chong':
                return this.getPrompt('CHONG_DEALER', params);

            case 'universal':
            case 'universal-template':
            case 'universal_template':
            case 'auto':
            case 'auto-detect':
            case 'auto_detect':
            case 'other':
            case 'fallback':
            default:
                return this.getPrompt('UNIVERSAL_FALLBACK', params);
        }
    }



    /**
     * 获取图片OCR提示词
     * @param {string} currentDate - 当前日期
     * @returns {string} OCR提示词
     */
    getImageOCRPrompt(currentDate) {
        return this.getPrompt('IMAGE_OCR', { current_date: currentDate });
    }

    /**
     * 获取图片质量评估提示词
     * @returns {string} 质量评估提示词
     */
    getImageQualityPrompt() {
        return this.getPrompt('IMAGE_QUALITY');
    }

    /**
     * 获取订单号识别提示词
     * @param {string} input - 订单文本内容
     * @returns {string} 订单号识别提示词
     */
    getOrderNumberExtractionPrompt(input) {
        return this.getPrompt('ORDER_NUMBER_EXTRACTION', { input: input });
    }

    /**
     * 获取所有可用的提示词类型
     * @returns {Array} 提示词类型列表
     */
    getAvailableTypes() {
        return Object.keys(this.prompts).map(key => ({
            type: key,
            name: this.prompts[key].name,
            description: this.prompts[key].description
        }));
    }

    /**
     * 验证提示词参数
     * @param {string} type - 提示词类型
     * @param {Object} params - 参数对象
     * @returns {boolean} 验证结果
     */
    validateParams(type, params) {
        const promptConfig = this.prompts[type];
        if (!promptConfig) {
            return false;
        }

        // 检查必需的参数
        const requiredParams = this.extractRequiredParams(promptConfig.prompt);
        return requiredParams.every(param => params.hasOwnProperty(param));
    }

    /**
     * 从提示词中提取必需的参数
     * @param {string} prompt - 提示词内容
     * @returns {Array} 必需参数列表
     */
    extractRequiredParams(prompt) {
        const matches = prompt.match(/{([^}]+)}/g);
        if (!matches) return [];
        
        return matches.map(match => match.slice(1, -1));
    }
}

// 导出配置和工具类
if (typeof module !== 'undefined' && module.exports) {
    // Node.js 环境
    module.exports = {
        GEMINI_PROMPTS,
        PromptManager
    };
} else {
    // 浏览器环境
    window.GEMINI_PROMPTS = GEMINI_PROMPTS;
    window.PromptManager = PromptManager;
}

// 创建全局实例
const promptManager = new PromptManager();

// 使用示例
/*
// 1. 获取OTA类型识别提示词
const detectionPrompt = promptManager.getDetectionPrompt(orderContent);

// 2. 根据OTA类型获取处理提示词
const processingPrompt = promptManager.getOTAPrompt('chong-dealer', orderContent, '2024-12-20');

// 3. 获取图片OCR提示词
const ocrPrompt = promptManager.getImageOCRPrompt('2024-12-20');

// 4. 获取所有可用类型
const availableTypes = promptManager.getAvailableTypes();
console.log('可用的提示词类型:', availableTypes);
*/