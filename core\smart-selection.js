/**
 * @file smart-selection.js - 智能选择服务 v3.0 增强版
 * @description 实现基于订单内容的智能ID选择功能，包含机器学习、模糊匹配、动态评分等增强算法
 * <AUTHOR> IDE
 * @created_at 2024-12-19
 * @updated_at 2024-12-19
 * @version 3.0.0 - 算法精度增强版
 */

/**
 * @class DynamicApiSyncManager - 动态API数据同步管理器
 * @description 实现与GoMyHire API的实时数据同步，自动更新车型、服务类型、用户数据
 */
class DynamicApiSyncManager {
    constructor() {
        this.syncInterval = 30 * 60 * 1000; // 30分钟同步一次
        this.lastSyncTime = null;
        this.syncStatus = 'idle';
        this.retryCount = 0;
        this.maxRetries = 3;
        
        // API端点配置
        this.apiEndpoints = {
            backendUsers: '/api/backend-users',
            subCategories: '/api/sub-categories', 
            carTypes: '/api/car-types'
        };
        
        // 同步状态监听器
        this.statusListeners = [];
        
        // 启动定时同步
        this.startAutoSync();
    }
    
    /**
     * @function startAutoSync - 启动自动同步机制
     */
    startAutoSync() {
        // 首次同步
        this.syncAllData();
        
        // 定时同步
        this.syncTimer = setInterval(() => {
            this.syncAllData();
        }, this.syncInterval);
        
        console.log('DynamicApiSync', '自动同步机制已启动', {
            interval: this.syncInterval,
            endpoints: Object.keys(this.apiEndpoints)
        });
    }
    
    /**
     * @function syncAllData - 同步所有API数据
     * @returns {Promise<Object>} 同步结果
     */
    async syncAllData() {
        try {
            this.syncStatus = 'syncing';
            this.notifyStatusChange('sync_started');
            
            console.log('DynamicApiSync', '开始API数据同步');
            
            // 并行获取所有数据
            const [backendUsers, subCategories, carTypes] = await Promise.all([
                this.fetchBackendUsers(),
                this.fetchSubCategories(),
                this.fetchCarTypes()
            ]);
            
            // 验证数据完整性
            const validationResult = this.validateSyncedData({
                backendUsers,
                subCategories, 
                carTypes
            });
            
            if (validationResult.valid) {
                // 更新应用状态
                this.updateAppState({
                    backendUsers,
                    subCategories,
                    carTypes
                });
                
                this.lastSyncTime = Date.now();
                this.syncStatus = 'success';
                this.retryCount = 0;
                
                console.log('DynamicApiSync', 'API数据同步成功', {
                    backendUsers: backendUsers.length,
                    subCategories: subCategories.length,
                    carTypes: carTypes.length,
                    timestamp: new Date().toISOString()
                });
                
                this.notifyStatusChange('sync_success', {
                    backendUsers: backendUsers.length,
                    subCategories: subCategories.length,
                    carTypes: carTypes.length
                });
                
                return {
                    success: true,
                    data: { backendUsers, subCategories, carTypes },
                    timestamp: this.lastSyncTime
                };
            } else {
                throw new Error(`数据验证失败: ${validationResult.errors.join(', ')}`);
            }
            
        } catch (error) {
            console.error('DynamicApiSync', 'API数据同步失败', error);
            
            this.syncStatus = 'error';
            this.retryCount++;
            
            this.notifyStatusChange('sync_error', {
                error: error.message,
                retryCount: this.retryCount
            });
            
            // 重试机制
            if (this.retryCount < this.maxRetries) {
                console.log('DynamicApiSync', `将在30秒后进行第${this.retryCount + 1}次重试`);
                setTimeout(() => this.syncAllData(), 30000);
            }
            
            return {
                success: false,
                error: error.message,
                retryCount: this.retryCount
            };
        }
    }
    
    /**
     * @function fetchBackendUsers - 获取后台用户数据
     * @returns {Promise<Array>} 用户数据
     */
    async fetchBackendUsers() {
        try {
            // 检查应用状态中是否已有数据
            const app = window.app;
            if (app && app.appState && app.appState.backendUsers) {
                console.log('DynamicApiSync', '使用应用状态中的后台用户数据');
                return app.appState.backendUsers;
            }
            
            // 模拟API调用 - 实际项目中替换为真实API
            console.log('DynamicApiSync', '模拟获取后台用户数据');
            
            // 基于gomyhire api id.md中的真实数据
            const mockUsers = [
                { "id": 1, "name": "Super Admin", "phone": "0162234711", "role": "Super Admin" },
                { "id": 105, "name": "Mei Kwan", "phone": null, "role": "Super Admin" },
                { "id": 22, "name": "Zahidah1", "phone": "0132456789", "role": "Operator" },
                { "id": 106, "name": "admin", "phone": null, "role": "Operator" },
                { "id": 108, "name": "meikwan", "phone": "0123456789", "role": "Operator" },
                { "id": 143, "name": "Kk", "phone": "012", "role": "Operator" },
                { "id": 206, "name": "OperatorRinglee", "phone": "0181122334", "role": "Operator" },
                { "id": 362, "name": "Kcy", "phone": null, "role": "Operator" },
                { "id": 110, "name": "Sub Admin", "phone": "0162234711", "role": "Sub_Admin" },
                { "id": 229, "name": "test", "phone": null, "role": "Sub_Admin" },
                { "id": 338, "name": "jcy1", "phone": null, "role": "Sub_Admin" },
                { "id": 163, "name": "Kok1", "phone": null, "role": "Sub_Operator" }
            ];
            
            return mockUsers;
            
        } catch (error) {
            console.error('DynamicApiSync', '获取后台用户数据失败', error);
            throw error;
        }
    }
    
    /**
     * @function fetchSubCategories - 获取子分类数据
     * @returns {Promise<Array>} 子分类数据
     */
    async fetchSubCategories() {
        try {
            const app = window.app;
            if (app && app.appState && app.appState.subCategories) {
                console.log('DynamicApiSync', '使用应用状态中的子分类数据');
                return app.appState.subCategories;
            }
            
            console.log('DynamicApiSync', '模拟获取子分类数据');
            
            // 基于gomyhire api id.md中的真实数据
            const mockSubCategories = [
                {
                    "id": 7,
                    "main_category": "Airport",
                    "name": "Pickup",
                    "preset_data": {
                        "order_type": "pickup",
                        "ota": null,
                        "driving_region": "KL - RM - Kuala Lumpur",
                        "languages": ["JP - Japanese", "MY - Malay", "CN - Chinese", "EN - English", "KR - Korean", "TML - Tamil"],
                        "extra_requirement": "Pickup at the right time please!"
                    },
                    "required_fields": ["Customer Name", "Customer Contact", "Driving Region", "Pickup Address", "Pickup Date", "Pickup Time", "Destination Address", "Passenger Number", "Driver Fee"]
                },
                {
                    "id": 8,
                    "main_category": "Airport",
                    "name": "Dropoff",
                    "preset_data": {
                        "order_type": "dropoff",
                        "ota": null,
                        "driving_region": "KL - RM - Kuala Lumpur",
                        "languages": ["JP - Japanese", "MY - Malay", "CN - Chinese", "EN - English"],
                        "extra_requirement": null
                    },
                    "required_fields": ["Customer Name", "Customer Contact", "Driving Region", "Pickup Address", "Pickup Date", "Pickup Time", "Destination Address", "Passenger Number", "Driver Fee"]
                },
                {
                    "id": 9,
                    "main_category": "Chartered",
                    "name": "KL to genting",
                    "preset_data": {
                        "order_type": "charter",
                        "ota": null,
                        "driving_region": null,
                        "languages": ["EN - English"],
                        "extra_requirement": "asdafdsgghfdfgdjhfygfcvxfgdtgcbncbncghfgfhcvbncvb..."
                    },
                    "required_fields": ["Customer Name", "Customer Contact", "Driving Region", "Pickup Address", "Pickup Date", "Pickup Time", "Destination Address", "Passenger Number", "Driver Fee"]
                },
                {
                    "id": 43,
                    "main_category": "Chartered",
                    "name": "Charter",
                    "preset_data": {
                        "order_type": "charter",
                        "ota": null,
                        "driving_region": null,
                        "languages": [],
                        "extra_requirement": null
                    },
                    "required_fields": ["Customer Name", "Customer Contact", "Driving Region", "Pickup Address", "Pickup Date", "Pickup Time", "Destination Address", "Passenger Number", "Driver Fee"]
                }
            ];
            
            return mockSubCategories;
            
        } catch (error) {
            console.error('DynamicApiSync', '获取子分类数据失败', error);
            throw error;
        }
    }
    
    /**
     * @function fetchCarTypes - 获取车型数据
     * @returns {Promise<Array>} 车型数据
     */
    async fetchCarTypes() {
        try {
            const app = window.app;
            if (app && app.appState && app.appState.carTypes) {
                console.log('DynamicApiSync', '使用应用状态中的车型数据');
                return app.appState.carTypes;
            }
            
            console.log('DynamicApiSync', '模拟获取车型数据');
            
            // 基于gomyhire api id.md中的真实数据
            const mockCarTypes = [
                { "id": 5, "type": "Compact 5 Seater", "seat_number": 4, "priority": 1 },
                { "id": 6, "type": "Comfort 5 Seater", "seat_number": 4, "priority": 2 },
                { "id": 15, "type": "Mid Size SUV", "seat_number": 7, "priority": 3 },
                { "id": 16, "type": "Standard Size MPV", "seat_number": 6, "priority": 4 },
                { "id": 31, "type": "Luxury Mpv", "seat_number": 6, "priority": 5 },
                { "id": 32, "type": "Alphard/Velfire", "seat_number": 6, "priority": 6 },
                { "id": 20, "type": "10 Seater MPV / Van", "seat_number": 9, "priority": 7 },
                { "id": 30, "type": "12 Seater MPV", "seat_number": 11, "priority": 8 },
                { "id": 23, "type": "14 Seater Van", "seat_number": 12, "priority": 9 },
                { "id": 24, "type": "18 Seater Van", "seat_number": 16, "priority": 10 },
                { "id": 25, "type": "30 Seat Mini Bus", "seat_number": 30, "priority": 11 },
                { "id": 26, "type": "44 Seater Bus", "seat_number": 44, "priority": 12 },
                { "id": 34, "type": "Please Refer Live Chat", "seat_number": 1, "priority": 13 }
            ];
            
            return mockCarTypes;
            
        } catch (error) {
            console.error('DynamicApiSync', '获取车型数据失败', error);
            throw error;
        }
    }
    
    /**
     * @function validateSyncedData - 验证同步的数据
     * @param {Object} data - 同步的数据
     * @returns {Object} 验证结果
     */
    validateSyncedData(data) {
        const errors = [];
        
        // 验证后台用户数据
        if (!Array.isArray(data.backendUsers) || data.backendUsers.length === 0) {
            errors.push('后台用户数据无效或为空');
        } else {
            const invalidUsers = data.backendUsers.filter(user => !user.id || !user.name);
            if (invalidUsers.length > 0) {
                errors.push(`发现${invalidUsers.length}个无效用户记录`);
            }
        }
        
        // 验证子分类数据
        if (!Array.isArray(data.subCategories) || data.subCategories.length === 0) {
            errors.push('子分类数据无效或为空');
        } else {
            const invalidCategories = data.subCategories.filter(cat => !cat.id || !cat.name);
            if (invalidCategories.length > 0) {
                errors.push(`发现${invalidCategories.length}个无效分类记录`);
            }
        }
        
        // 验证车型数据
        if (!Array.isArray(data.carTypes) || data.carTypes.length === 0) {
            errors.push('车型数据无效或为空');
        } else {
            const invalidCarTypes = data.carTypes.filter(car => !car.id || !car.type);
            if (invalidCarTypes.length > 0) {
                errors.push(`发现${invalidCarTypes.length}个无效车型记录`);
            }
        }
        
        return {
            valid: errors.length === 0,
            errors: errors
        };
    }
    
    /**
     * @function updateAppState - 更新应用状态
     * @param {Object} data - 新数据
     */
    updateAppState(data) {
        try {
            const app = window.app;
            if (app && app.appState) {
                // 更新应用状态
                app.appState.backendUsers = data.backendUsers;
                app.appState.subCategories = data.subCategories;
                app.appState.carTypes = data.carTypes;
                
                // 触发智能选择服务更新映射表
                if (window.smartSelection) {
                    window.smartSelection.updateMappingFromAppState();
                }
                
                console.log('DynamicApiSync', '应用状态已更新', {
                    backendUsersCount: data.backendUsers.length,
                    subCategoriesCount: data.subCategories.length,
                    carTypesCount: data.carTypes.length
                });
            }
        } catch (error) {
            console.error('DynamicApiSync', '更新应用状态失败', error);
        }
    }
    
    /**
     * @function addStatusListener - 添加状态监听器
     * @param {Function} listener - 监听器函数
     */
    addStatusListener(listener) {
        this.statusListeners.push(listener);
    }
    
    /**
     * @function notifyStatusChange - 通知状态变更
     * @param {string} status - 状态
     * @param {Object} data - 附加数据
     */
    notifyStatusChange(status, data = {}) {
        this.statusListeners.forEach(listener => {
            try {
                listener(status, data);
            } catch (error) {
                console.error('DynamicApiSync', '状态监听器执行失败', error);
            }
        });
    }
    
    /**
     * @function forceSync - 强制立即同步
     * @returns {Promise<Object>} 同步结果
     */
    async forceSync() {
        console.log('DynamicApiSync', '强制执行数据同步');
        return await this.syncAllData();
    }
    
    /**
     * @function getSyncStatus - 获取同步状态
     * @returns {Object} 同步状态信息
     */
    getSyncStatus() {
        return {
            status: this.syncStatus,
            lastSyncTime: this.lastSyncTime,
            retryCount: this.retryCount,
            nextSyncTime: this.lastSyncTime ? this.lastSyncTime + this.syncInterval : null
        };
    }
    
    /**
     * @function stopAutoSync - 停止自动同步
     */
    stopAutoSync() {
        if (this.syncTimer) {
            clearInterval(this.syncTimer);
            this.syncTimer = null;
            console.log('DynamicApiSync', '自动同步已停止');
        }
    }
}

/**
 * @class EnhancedMatchingEngine - 增强匹配引擎
 * @description 提供模糊匹配、同义词匹配、语义相似度计算等高级匹配功能
 */
class EnhancedMatchingEngine {
    constructor() {
        // 同义词词典 - 支持中英文
        this.synonymDict = {
            // 车型同义词
            'sedan': ['轿车', '小车', '私家车', 'car', 'saloon'],
            'suv': ['越野车', '运动型多用途车', 'sport utility vehicle'],
            'mpv': ['商务车', '多用途车', 'multi-purpose vehicle', '面包车'],
            'van': ['面包车', '厢式车', '货车', 'minivan'],
            'bus': ['大巴', '巴士', '客车', 'coach'],
            'luxury': ['豪华', '高端', '奢华', 'premium', 'deluxe', 'vip'],
            
            // 服务类型同义词
            'pickup': ['接机', '接送', '迎接', 'pick up', 'collect'],
            'dropoff': ['送机', '送达', '护送', 'drop off', 'deliver'],
            'charter': ['包车', '租车', '专车', 'private hire', 'exclusive'],
            'transfer': ['转移', '接驳', '中转', 'transport'],
            
            // 地点同义词
            'airport': ['机场', '航站楼', '候机楼', 'terminal'],
            'hotel': ['酒店', '宾馆', '旅馆', 'resort', 'accommodation'],
            'mall': ['商场', '购物中心', 'shopping center', 'plaza'],
            
            // 时间同义词
            'urgent': ['紧急', '急需', '马上', 'asap', 'immediately'],
            'flexible': ['灵活', '可调整', '弹性', 'adjustable']
        };
        
        // 拼音匹配表（扩展版）
        this.pinyinMap = {
            // 基础拼音
            'jie': '接', 'song': '送', 'che': '车', 'ji': '机',
            'bao': '包', 'hao': '豪', 'hua': '华', 'shang': '商',
            'wu': '务', 'jing': '经', 'ji': '济', 'shu': '舒',
            'shi': '适', 'da': '大', 'xiao': '小', 'zhong': '中',
            // 扩展拼音
            'jie机': '接机', 'song机': '送机', 'bao车': '包车', 
            'lv游': '旅游', 'jing点': '景点', 'hao华': '豪华',
            'shang务': '商务', 'an全': '安全', 'kuai捷': '快捷',
            'wen度': '温度', 'su度': '速度', 'gao铁': '高铁',
            'fei机': '飞机', 'huo车': '火车', 'lu线': '路线'
        };
        
        // 缩写匹配表
        this.abbreviationMap = {
            // 英文缩写
            'suv': 'sport utility vehicle',
            'mpv': 'multi-purpose vehicle', 
            'vip': 'very important person',
            'api': 'application programming interface',
            'ota': 'online travel agency',
            'gps': 'global positioning system',
            'ac': 'air conditioning',
            'atm': 'automated teller machine',
            'kl': 'kuala lumpur',
            'pj': 'petaling jaya',
            'sg': 'singapore',
            
            // 中文缩写
            '北京': 'bj',
            '上海': 'sh', 
            '广州': 'gz',
            '深圳': 'sz',
            '成都': 'cd',
            '杭州': 'hz',
            '南京': 'nj',
            '武汉': 'wh',
            '西安': 'xa',
            '重庆': 'cq',
            '天津': 'tj',
            '青岛': 'qd',
            '大连': 'dl',
            '厦门': 'xm',
            '福州': 'fz',
            '合肥': 'hf',
            '长沙': 'cs',
            '郑州': 'zz',
            '济南': 'jn',
            '太原': 'ty',
            '石家庄': 'sjz',
            '沈阳': 'sy',
            '长春': 'cc',
            '哈尔滨': 'hrb',
            '昆明': 'km',
            '贵阳': 'gy',
            '兰州': 'lz',
            '银川': 'yc',
            '西宁': 'xn',
            '乌鲁木齐': 'wlmq',
            '拉萨': 'ls',
            '海口': 'hk',
            '三亚': 'sy',
            '南宁': 'nn',
            '桂林': 'gl',
            '南昌': 'nc',
            '呼和浩特': 'hhht'
        };
        
        // 语义相似度词汇表（扩展版）
        this.semanticGroups = {
            transportation: {
                vehicles: ['车', 'car', 'vehicle', 'automobile', '汽车', '车辆'],
                types: ['sedan', 'suv', 'mpv', 'van', 'bus', '轿车', '越野车', '商务车', '面包车', '巴士'],
                actions: ['drive', 'ride', 'travel', '驾驶', '乘坐', '出行', '运输']
            },
            service: {
                types: ['pickup', 'dropoff', 'charter', 'transfer', '接送', '包车', '转乘', '服务'],
                quality: ['luxury', 'premium', 'standard', 'economy', '豪华', '高级', '标准', '经济'],
                time: ['urgent', 'immediate', 'scheduled', '紧急', '立即', '预约', '定时']
            },
            location: {
                airport: ['airport', 'terminal', 'runway', '机场', '航站楼', '候机楼'],
                hotel: ['hotel', 'resort', 'accommodation', '酒店', '度假村', '住宿'],
                attraction: ['attraction', 'sightseeing', 'tour', '景点', '观光', '旅游']
            }
        };
        
        // 匹配权重配置（增强版）
        this.matchWeights = {
            exactMatch: 1.0,        // 精确匹配
            synonymMatch: 0.9,      // 同义词匹配
            pinyinMatch: 0.85,      // 拼音匹配
            abbreviationMatch: 0.8, // 缩写匹配
            fuzzyMatch: 0.75,       // 模糊匹配
            semanticMatch: 0.7,     // 语义匹配
            partialMatch: 0.6,      // 部分匹配
            contextMatch: 0.5,      // 上下文匹配
            soundexMatch: 0.4       // 音似匹配
        };
    }
    
    /**
     * @function calculateStringDistance - 计算字符串编辑距离
     * @param {string} str1 - 字符串1
     * @param {string} str2 - 字符串2
     * @returns {number} 编辑距离
     */
    calculateStringDistance(str1, str2) {
        if (!str1 || !str2) return Infinity;
        
        const len1 = str1.length;
        const len2 = str2.length;
        
        // 创建距离矩阵
        const matrix = Array(len1 + 1).fill().map(() => Array(len2 + 1).fill(0));
        
        // 初始化第一行和第一列
        for (let i = 0; i <= len1; i++) matrix[i][0] = i;
        for (let j = 0; j <= len2; j++) matrix[0][j] = j;
        
        // 计算编辑距离
        for (let i = 1; i <= len1; i++) {
            for (let j = 1; j <= len2; j++) {
                const cost = str1[i - 1] === str2[j - 1] ? 0 : 1;
                matrix[i][j] = Math.min(
                    matrix[i - 1][j] + 1,      // 删除
                    matrix[i][j - 1] + 1,      // 插入
                    matrix[i - 1][j - 1] + cost // 替换
                );
            }
        }
        
        return matrix[len1][len2];
    }
    
    /**
     * @function calculateSimilarity - 计算相似度评分
     * @param {string} source - 源字符串
     * @param {string} target - 目标字符串
     * @returns {number} 相似度评分 (0.0-1.0)
     */
    calculateSimilarity(source, target) {
        if (!source || !target) return 0;
        
        const src = source.toLowerCase().trim();
        const tgt = target.toLowerCase().trim();
        
        // 精确匹配
        if (src === tgt) return this.matchWeights.exactMatch;
        
        // 包含匹配
        if (src.includes(tgt) || tgt.includes(src)) {
            const longerLength = Math.max(src.length, tgt.length);
            const shorterLength = Math.min(src.length, tgt.length);
            return this.matchWeights.partialMatch * (shorterLength / longerLength);
        }
        
        // 编辑距离匹配
        const distance = this.calculateStringDistance(src, tgt);
        const maxLength = Math.max(src.length, tgt.length);
        if (maxLength === 0) return 0;
        
        const similarity = 1 - (distance / maxLength);
        return similarity * this.matchWeights.fuzzyMatch;
    }
    
    /**
     * @function findSynonyms - 查找同义词
     * @param {string} word - 输入词汇
     * @returns {string[]} 同义词列表
     */
    findSynonyms(word) {
        const normalizedWord = word.toLowerCase().trim();
        const synonyms = [];
        
        // 直接查找
        if (this.synonymDict[normalizedWord]) {
            synonyms.push(...this.synonymDict[normalizedWord]);
        }
        
        // 反向查找
        for (const [key, values] of Object.entries(this.synonymDict)) {
            if (values.some(synonym => synonym.toLowerCase() === normalizedWord)) {
                synonyms.push(key, ...values);
            }
        }
        
        // 去重并返回
        return [...new Set(synonyms)].filter(syn => syn.toLowerCase() !== normalizedWord);
    }
    
    /**
     * @function enhancedKeywordMatch - 增强关键词匹配算法 v2.0
     * @param {string} text - 搜索文本
     * @param {string} keyword - 关键词
     * @returns {object} 匹配结果，包含多种匹配策略的结果
     */
    enhancedKeywordMatch(text, keyword) {
        if (!text || !keyword) return { matched: false, score: 0 };
        
        const normalizedText = text.toLowerCase().trim();
        const normalizedKeyword = keyword.toLowerCase().trim();
        
        // 存储所有匹配结果
        const matchResults = [];
        
        // 1. 精确匹配
        if (normalizedText.includes(normalizedKeyword)) {
            matchResults.push({
                matched: true,
                score: this.matchWeights.exactMatch,
                method: 'exact_match',
                matchedTerm: normalizedKeyword,
                confidence: 1.0
            });
        }
        
        // 2. 同义词匹配
        const synonyms = this.findSynonyms(normalizedKeyword);
        for (const synonym of synonyms) {
            if (normalizedText.includes(synonym.toLowerCase())) {
                matchResults.push({
                    matched: true,
                    score: this.matchWeights.synonymMatch,
                    method: 'synonym_match',
                    matchedTerm: synonym,
                    originalKeyword: normalizedKeyword,
                    confidence: 0.9
                });
                break; // 只取第一个匹配的同义词
            }
        }
        
        // 3. 拼音匹配（新增）
        const pinyinResult = this.performPinyinMatch(normalizedText, normalizedKeyword);
        if (pinyinResult.matched) {
            matchResults.push({
                ...pinyinResult,
                score: this.matchWeights.pinyinMatch,
                confidence: 0.85
            });
        }
        
        // 4. 缩写匹配（新增）
        const abbreviationResult = this.performAbbreviationMatch(normalizedText, normalizedKeyword);
        if (abbreviationResult.matched) {
            matchResults.push({
                ...abbreviationResult,
                score: this.matchWeights.abbreviationMatch,
                confidence: 0.8
            });
        }
        
        // 5. 语义匹配（新增）
        const semanticResult = this.performSemanticMatch(normalizedText, normalizedKeyword);
        if (semanticResult.matched) {
            matchResults.push({
                ...semanticResult,
                score: this.matchWeights.semanticMatch,
                confidence: 0.7
            });
        }
        
        // 6. 模糊匹配（增强版）
        const similarity = this.calculateSimilarity(normalizedText, normalizedKeyword);
        if (similarity > 0.6) {
            matchResults.push({
                matched: true,
                score: similarity * this.matchWeights.fuzzyMatch,
                method: 'enhanced_fuzzy_match',
                matchedTerm: normalizedKeyword,
                similarity: similarity,
                confidence: similarity
            });
        }
        
        // 7. 部分词匹配（优化版）
        const partialResult = this.performPartialWordMatch(normalizedText, normalizedKeyword);
        if (partialResult.matched) {
            matchResults.push({
                ...partialResult,
                score: partialResult.score * this.matchWeights.partialMatch,
                confidence: partialResult.confidence
            });
        }
        
        // 8. 音似匹配（新增）
        const soundexResult = this.performSoundexMatch(normalizedText, normalizedKeyword);
        if (soundexResult.matched) {
            matchResults.push({
                ...soundexResult,
                score: this.matchWeights.soundexMatch,
                confidence: 0.4
            });
        }
        
        // 选择最佳匹配结果
        if (matchResults.length > 0) {
            // 按评分排序，选择最佳结果
            matchResults.sort((a, b) => b.score - a.score);
            const bestResult = matchResults[0];
            
            // 添加所有匹配方法的信息
            bestResult.allMatches = matchResults.map(r => ({
                method: r.method,
                score: r.score,
                confidence: r.confidence
            }));
            
            return bestResult;
        }
        
        return { matched: false, score: 0, allMatches: [] };
    }
    
    /**
     * @function performPinyinMatch - 执行拼音匹配
     * @param {string} text - 搜索文本
     * @param {string} keyword - 关键词
     * @returns {object} 拼音匹配结果
     */
    performPinyinMatch(text, keyword) {
        // 检查拼音映射
        for (const [pinyin, chinese] of Object.entries(this.pinyinMap)) {
            // 拼音转中文匹配
            if (keyword.includes(pinyin) && text.includes(chinese)) {
                return {
                    matched: true,
                    method: 'pinyin_to_chinese_match',
                    matchedTerm: chinese,
                    pinyinTerm: pinyin,
                    originalKeyword: keyword
                };
            }
            
            // 中文转拼音匹配
            if (keyword.includes(chinese) && text.includes(pinyin)) {
                return {
                    matched: true,
                    method: 'chinese_to_pinyin_match',
                    matchedTerm: pinyin,
                    chineseTerm: chinese,
                    originalKeyword: keyword
                };
            }
        }
        
        return { matched: false };
    }
    
    /**
     * @function performAbbreviationMatch - 执行缩写匹配
     * @param {string} text - 搜索文本
     * @param {string} keyword - 关键词
     * @returns {object} 缩写匹配结果
     */
    performAbbreviationMatch(text, keyword) {
        // 检查缩写映射
        for (const [abbr, full] of Object.entries(this.abbreviationMap)) {
            // 缩写转全称匹配
            if (keyword.includes(abbr) && text.includes(full.toLowerCase())) {
                return {
                    matched: true,
                    method: 'abbreviation_to_full_match',
                    matchedTerm: full,
                    abbreviation: abbr,
                    originalKeyword: keyword
                };
            }
            
            // 全称转缩写匹配
            if (keyword.includes(full.toLowerCase()) && text.includes(abbr)) {
                return {
                    matched: true,
                    method: 'full_to_abbreviation_match',
                    matchedTerm: abbr,
                    fullTerm: full,
                    originalKeyword: keyword
                };
            }
        }
        
        return { matched: false };
    }
    
    /**
     * @function performSemanticMatch - 执行语义匹配
     * @param {string} text - 搜索文本
     * @param {string} keyword - 关键词
     * @returns {object} 语义匹配结果
     */
    performSemanticMatch(text, keyword) {
        // 遍历语义组
        for (const [category, groups] of Object.entries(this.semanticGroups)) {
            for (const [groupName, words] of Object.entries(groups)) {
                // 检查关键词是否在语义组中
                const keywordInGroup = words.some(word => 
                    keyword.includes(word.toLowerCase()));
                
                if (keywordInGroup) {
                    // 检查文本中是否有同组的其他词汇
                    for (const word of words) {
                        if (text.includes(word.toLowerCase()) && 
                            !keyword.includes(word.toLowerCase())) {
                            return {
                                matched: true,
                                method: 'semantic_group_match',
                                matchedTerm: word,
                                semanticCategory: category,
                                semanticGroup: groupName,
                                originalKeyword: keyword
                            };
                        }
                    }
                }
            }
        }
        
        return { matched: false };
    }
    
    /**
     * @function performPartialWordMatch - 执行部分词匹配（优化版）
     * @param {string} text - 搜索文本
     * @param {string} keyword - 关键词
     * @returns {object} 部分词匹配结果
     */
    performPartialWordMatch(text, keyword) {
        const textWords = text.split(/\s+/);
        const keywordWords = keyword.split(/\s+/);
        
        let matchedWords = 0;
        const matchedTerms = [];
        
        for (const keyWord of keywordWords) {
            for (const textWord of textWords) {
                const similarity = this.calculateSimilarity(textWord, keyWord);
                if (similarity > 0.7) {
                    matchedWords++;
                    matchedTerms.push({
                        textWord,
                        keyWord,
                        similarity
                    });
                    break;
                }
            }
        }
        
        if (matchedWords > 0) {
            const partialScore = matchedWords / keywordWords.length;
            const avgSimilarity = matchedTerms.reduce((sum, term) => 
                sum + term.similarity, 0) / matchedTerms.length;
            
            return {
                matched: true,
                method: 'enhanced_partial_match',
                matchedTerm: keyword,
                matchedWords: matchedWords,
                totalWords: keywordWords.length,
                partialScore: partialScore,
                avgSimilarity: avgSimilarity,
                score: partialScore * avgSimilarity,
                confidence: avgSimilarity,
                matchDetails: matchedTerms
            };
        }
        
        return { matched: false };
    }
    
    /**
     * @function performSoundexMatch - 执行音似匹配
     * @param {string} text - 搜索文本
     * @param {string} keyword - 关键词
     * @returns {object} 音似匹配结果
     */
    performSoundexMatch(text, keyword) {
        try {
            // 简化的中文音似匹配
            const chineseSoundMap = {
                // 声母相似
                'zh': 'z', 'ch': 'c', 'sh': 's',
                'j': 'z', 'q': 'c', 'x': 's',
                // 韵母相似
                'an': 'ang', 'en': 'eng', 'in': 'ing',
                'un': 'ung', 'uan': 'uang'
            };
            
            // 检查音似匹配
            for (const [sound1, sound2] of Object.entries(chineseSoundMap)) {
                if (keyword.includes(sound1) && text.includes(sound2)) {
                    return {
                        matched: true,
                        method: 'chinese_soundex_match',
                        matchedTerm: sound2,
                        soundPattern: sound1,
                        originalKeyword: keyword
                    };
                }
                
                if (keyword.includes(sound2) && text.includes(sound1)) {
                    return {
                        matched: true,
                        method: 'chinese_soundex_match',
                        matchedTerm: sound1,
                        soundPattern: sound2,
                        originalKeyword: keyword
                    };
                }
            }
            
            return { matched: false };
            
        } catch (error) {
            return { matched: false };
        }
    }
}

/**
 * @class IntelligentLearningEngine - 智能学习引擎
 * @description 从历史数据中学习，自动优化匹配算法和权重
 */
class IntelligentLearningEngine {
    constructor() {
        this.learningData = this.loadLearningData();
        this.performanceMetrics = {
            totalSelections: 0,
            successfulSelections: 0,
            failedSelections: 0,
            accuracyRate: 0,
            lastUpdate: Date.now()
        };
        
        // 动态权重调整因子
        this.dynamicWeights = {
            passengerCount: 0.4,
            keywordMatch: 0.3,
            serviceType: 0.2,
            contextual: 0.1
        };
        
        // 学习阈值
        this.learningThreshold = {
            minSamples: 10,          // 最少样本数
            confidenceThreshold: 0.8, // 置信度阈值
            adaptionRate: 0.1        // 自适应学习率
        };
    }
    
    /**
     * @function loadLearningData - 加载学习数据
     * @returns {object} 学习数据
     */
    loadLearningData() {
        try {
            const savedData = localStorage.getItem('smartSelection_learningData');
            return savedData ? JSON.parse(savedData) : {
                successfulMatches: [],
                failedMatches: [],
                userPreferences: {},
                patternRecognition: {}
            };
        } catch (error) {
            console.warn('智能学习引擎: 加载学习数据失败', error);
            return {
                successfulMatches: [],
                failedMatches: [],
                userPreferences: {},
                patternRecognition: {}
            };
        }
    }
    
    /**
     * @function saveLearningData - 保存学习数据
     */
    saveLearningData() {
        try {
            localStorage.setItem('smartSelection_learningData', JSON.stringify(this.learningData));
        } catch (error) {
            console.warn('智能学习引擎: 保存学习数据失败', error);
        }
    }
    
    /**
     * @function recordSelection - 记录选择结果
     * @param {object} selectionRecord - 选择记录
     */
    recordSelection(selectionRecord) {
        const record = {
            ...selectionRecord,
            timestamp: Date.now(),
            sessionId: this.getSessionId()
        };
        
        if (selectionRecord.success) {
            this.learningData.successfulMatches.push(record);
            this.performanceMetrics.successfulSelections++;
        } else {
            this.learningData.failedMatches.push(record);
            this.performanceMetrics.failedSelections++;
        }
        
        this.performanceMetrics.totalSelections++;
        this.updateAccuracyRate();
        
        // 限制记录数量，避免内存过载
        if (this.learningData.successfulMatches.length > 1000) {
            this.learningData.successfulMatches = this.learningData.successfulMatches.slice(-500);
        }
        if (this.learningData.failedMatches.length > 500) {
            this.learningData.failedMatches = this.learningData.failedMatches.slice(-250);
        }
        
        this.saveLearningData();
        this.adaptWeights();
    }
    
    /**
     * @function getSessionId - 获取会话ID
     * @returns {string} 会话ID
     */
    getSessionId() {
        if (!window.sessionStorage.getItem('smartSelection_sessionId')) {
            window.sessionStorage.setItem('smartSelection_sessionId', 
                'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9));
        }
        return window.sessionStorage.getItem('smartSelection_sessionId');
    }
    
    /**
     * @function updateAccuracyRate - 更新准确率
     */
    updateAccuracyRate() {
        if (this.performanceMetrics.totalSelections > 0) {
            this.performanceMetrics.accuracyRate = 
                this.performanceMetrics.successfulSelections / this.performanceMetrics.totalSelections;
        }
        this.performanceMetrics.lastUpdate = Date.now();
    }
    
    /**
     * @function adaptWeights - 自适应权重调整
     */
    adaptWeights() {
        if (this.learningData.successfulMatches.length < this.learningThreshold.minSamples) {
            return; // 样本数不足，不进行调整
        }
        
        // 分析成功匹配的模式
        const recentSuccess = this.learningData.successfulMatches.slice(-50);
        const methodStats = {};
        
        recentSuccess.forEach(record => {
            const method = record.method || 'unknown';
            if (!methodStats[method]) {
                methodStats[method] = { count: 0, totalConfidence: 0 };
            }
            methodStats[method].count++;
            methodStats[method].totalConfidence += (record.confidence || 0.5);
        });
        
        // 根据成功率调整权重
        const totalSamples = recentSuccess.length;
        for (const [method, stats] of Object.entries(methodStats)) {
            const successRate = stats.count / totalSamples;
            const avgConfidence = stats.totalConfidence / stats.count;
            
            // 自适应调整对应的权重
            if (method.includes('passenger') && successRate > 0.8) {
                this.dynamicWeights.passengerCount = Math.min(0.6, 
                    this.dynamicWeights.passengerCount + this.learningThreshold.adaptionRate);
            }
            if (method.includes('keyword') && successRate > 0.8) {
                this.dynamicWeights.keywordMatch = Math.min(0.5, 
                    this.dynamicWeights.keywordMatch + this.learningThreshold.adaptionRate);
            }
        }
        
        // 权重归一化
        const totalWeight = Object.values(this.dynamicWeights).reduce((sum, weight) => sum + weight, 0);
        for (const key of Object.keys(this.dynamicWeights)) {
            this.dynamicWeights[key] = this.dynamicWeights[key] / totalWeight;
        }
    }
    
    /**
     * @function getOptimalWeight - 获取优化后的权重
     * @param {string} method - 方法名称
     * @returns {number} 权重值
     */
    getOptimalWeight(method) {
        const baseWeights = {
            'passenger_count_exact': this.dynamicWeights.passengerCount,
            'enhanced_keyword_match': this.dynamicWeights.keywordMatch,
            'service_mapping': this.dynamicWeights.serviceType,
            'contextual_analysis': this.dynamicWeights.contextual
        };
        
        return baseWeights[method] || 0.5;
    }
    
    /**
     * @function analyzePatterns - 分析匹配模式
     * @param {object} orderData - 订单数据
     * @returns {object} 模式分析结果
     */
    analyzePatterns(orderData) {
        const patterns = {
            suggestedMethod: null,
            confidence: 0,
            reasonCode: '',
            historicalAccuracy: 0
        };
        
        // 查找相似的历史成功案例
        const similarCases = this.findSimilarSuccessfulCases(orderData);
        
        if (similarCases.length > 0) {
            // 统计最常用的成功方法
            const methodCounts = {};
            similarCases.forEach(similarCase => {
                const method = similarCase.method || 'unknown';
                methodCounts[method] = (methodCounts[method] || 0) + 1;
            });
            
            // 找到最佳方法
            const bestMethod = Object.entries(methodCounts)
                .sort(([,a], [,b]) => b - a)[0];
            
            if (bestMethod) {
                patterns.suggestedMethod = bestMethod[0];
                patterns.confidence = bestMethod[1] / similarCases.length;
                patterns.historicalAccuracy = this.calculateMethodAccuracy(bestMethod[0]);
                patterns.reasonCode = `基于${similarCases.length}个相似成功案例的分析`;
            }
        }
        
        return patterns;
    }
    
    /**
     * @function findSimilarSuccessfulCases - 查找相似的成功案例
     * @param {object} orderData - 订单数据
     * @returns {array} 相似案例列表
     */
    findSimilarSuccessfulCases(orderData) {
        const similarCases = [];
        const passengerCount = parseInt(orderData.passenger_count) || 1;
        
        this.learningData.successfulMatches.forEach(record => {
            let similarity = 0;
            
            // 乘客数量相似度
            if (record.orderData && record.orderData.passenger_count) {
                const recordPassengers = parseInt(record.orderData.passenger_count) || 1;
                const passengerDiff = Math.abs(passengerCount - recordPassengers);
                similarity += passengerDiff <= 2 ? 0.4 : 0;
            }
            
            // 服务类型相似度
            if (record.orderData && orderData.service_type) {
                const recordService = (record.orderData.service_type || '').toLowerCase();
                const currentService = (orderData.service_type || '').toLowerCase();
                if (recordService === currentService) similarity += 0.3;
            }
            
            // 关键词相似度
            if (record.orderData) {
                const recordText = (record.orderData.remarks || '').toLowerCase();
                const currentText = (orderData.remarks || '').toLowerCase();
                if (recordText && currentText) {
                    const commonWords = recordText.split(' ').filter(word => 
                        currentText.includes(word) && word.length > 2);
                    similarity += Math.min(0.3, commonWords.length * 0.1);
                }
            }
            
            // 如果相似度足够高，加入结果
            if (similarity > 0.5) {
                similarCases.push({
                    ...record,
                    similarity: similarity
                });
            }
        });
        
        // 按相似度排序并返回最相似的案例
        return similarCases.sort((a, b) => b.similarity - a.similarity).slice(0, 10);
    }
    
    /**
     * @function calculateMethodAccuracy - 计算方法准确率
     * @param {string} method - 方法名称
     * @returns {number} 准确率
     */
    calculateMethodAccuracy(method) {
        const successCases = this.learningData.successfulMatches.filter(
            record => record.method === method);
        const failedCases = this.learningData.failedMatches.filter(
            record => record.method === method);
        
        const total = successCases.length + failedCases.length;
        return total > 0 ? successCases.length / total : 0;
    }
    
    /**
     * @function getPerformanceMetrics - 获取性能指标
     * @returns {object} 性能指标
     */
    getPerformanceMetrics() {
        return {
            ...this.performanceMetrics,
            dynamicWeights: { ...this.dynamicWeights },
            dataSize: {
                successfulMatches: this.learningData.successfulMatches.length,
                failedMatches: this.learningData.failedMatches.length
            }
        };
    }
}

/**
 * @class DynamicAccuracyCalculator - 动态精度计算器
 * @description 提供统一的精度计算和置信度评估功能
 */
class DynamicAccuracyCalculator {
    constructor(learningEngine) {
        this.learningEngine = learningEngine;
        
        // 精度计算因子
        this.accuracyFactors = {
            matchPrecision: 0.3,     // 匹配精确度
            methodReliability: 0.25,  // 方法可靠性
            historicalSuccess: 0.2,   // 历史成功率
            contextRelevance: 0.15,   // 上下文相关性
            dataQuality: 0.1         // 数据质量
        };
    }
    
    /**
     * @function calculateCompositeScore - 计算综合评分
     * @param {object} matchResult - 匹配结果
     * @param {object} context - 上下文信息
     * @returns {object} 综合评分结果
     */
    calculateCompositeScore(matchResult, context = {}) {
        const scores = {
            matchPrecision: this.calculateMatchPrecision(matchResult),
            methodReliability: this.calculateMethodReliability(matchResult.method),
            historicalSuccess: this.calculateHistoricalSuccess(matchResult.method, context),
            contextRelevance: this.calculateContextRelevance(matchResult, context),
            dataQuality: this.calculateDataQuality(context.orderData)
        };
        
        // 计算加权总分
        let compositeScore = 0;
        for (const [factor, weight] of Object.entries(this.accuracyFactors)) {
            compositeScore += (scores[factor] || 0) * weight;
        }
        
        // 应用学习引擎的动态权重调整
        const learningBoost = this.learningEngine.getOptimalWeight(matchResult.method);
        compositeScore = compositeScore * (0.7 + learningBoost * 0.3);
        
        return {
            compositeScore: Math.min(1.0, Math.max(0.0, compositeScore)),
            detailedScores: scores,
            confidence: this.calculateConfidence(compositeScore, scores),
            reliability: this.calculateReliability(matchResult, compositeScore)
        };
    }
    
    /**
     * @function calculateMatchPrecision - 计算匹配精确度
     * @param {object} matchResult - 匹配结果
     * @returns {number} 精确度评分
     */
    calculateMatchPrecision(matchResult) {
        const baseScore = matchResult.confidence || 0.5;
        
        // 根据匹配方法调整精确度
        const methodBonus = {
            'exact_match': 0.2,
            'passenger_count_exact': 0.15,
            'enhanced_keyword_match': 0.1,
            'synonym_match': 0.08,
            'fuzzy_match': 0.05,
            'default_fallback': -0.1
        };
        
        const bonus = methodBonus[matchResult.method] || 0;
        return Math.min(1.0, baseScore + bonus);
    }
    
    /**
     * @function calculateMethodReliability - 计算方法可靠性
     * @param {string} method - 匹配方法
     * @returns {number} 可靠性评分
     */
    calculateMethodReliability(method) {
        // 基于历史数据的方法可靠性评分
        const reliabilityMap = {
            'passenger_count_exact': 0.9,
            'enhanced_keyword_match': 0.85,
            'service_mapping': 0.8,
            'ota_mapping': 0.85,
            'fuzzy_match': 0.7,
            'contextual_analysis': 0.75,
            'default_fallback': 0.4
        };
        
        const baseReliability = reliabilityMap[method] || 0.6;
        const learningAdjustment = this.learningEngine.calculateMethodAccuracy(method);
        
        return (baseReliability + learningAdjustment) / 2;
    }
    
    /**
     * @function calculateHistoricalSuccess - 计算历史成功率
     * @param {string} method - 匹配方法
     * @param {object} context - 上下文信息
     * @returns {number} 历史成功率
     */
    calculateHistoricalSuccess(method, context) {
        const patterns = this.learningEngine.analyzePatterns(context.orderData || {});
        
        if (patterns.suggestedMethod === method) {
            return patterns.historicalAccuracy;
        }
        
        return this.learningEngine.calculateMethodAccuracy(method);
    }
    
    /**
     * @function calculateContextRelevance - 计算上下文相关性
     * @param {object} matchResult - 匹配结果
     * @param {object} context - 上下文信息
     * @returns {number} 相关性评分
     */
    calculateContextRelevance(matchResult, context) {
        let relevanceScore = 0.5; // 基础分
        
        const orderData = context.orderData || {};
        
        // 乘客数量与车型的匹配度
        if (matchResult.seats && orderData.passenger_count) {
            const passengers = parseInt(orderData.passenger_count) || 1;
            if (matchResult.seats >= passengers && matchResult.seats <= passengers + 2) {
                relevanceScore += 0.2; // 座位数刚好合适
            } else if (matchResult.seats >= passengers) {
                relevanceScore += 0.1; // 座位数足够
            }
        }
        
        // OTA平台相关性
        if (matchResult.method === 'ota_mapping' && orderData.ota) {
            relevanceScore += 0.2;
        }
        
        // 服务类型相关性
        if (matchResult.method === 'service_mapping' && orderData.service_type) {
            relevanceScore += 0.15;
        }
        
        // 时间敏感性
        if (orderData.flight_number && matchResult.method === 'flight_service') {
            relevanceScore += 0.15;
        }
        
        return Math.min(1.0, relevanceScore);
    }
    
    /**
     * @function calculateDataQuality - 计算数据质量
     * @param {object} orderData - 订单数据
     * @returns {number} 数据质量评分
     */
    calculateDataQuality(orderData = {}) {
        let qualityScore = 0;
        const maxScore = 1.0;
        
        // 检查关键字段的完整性
        const keyFields = ['customer_name', 'service_type', 'passenger_count'];
        const optionalFields = ['flight_number', 'pickup_location', 'destination', 'remarks'];
        
        keyFields.forEach(field => {
            if (orderData[field] && orderData[field].toString().trim()) {
                qualityScore += 0.25; // 每个关键字段25%
            }
        });
        
        optionalFields.forEach(field => {
            if (orderData[field] && orderData[field].toString().trim()) {
                qualityScore += 0.0625; // 每个可选字段6.25%
            }
        });
        
        return Math.min(maxScore, qualityScore);
    }
    
    /**
     * @function calculateConfidence - 计算置信度
     * @param {number} compositeScore - 综合评分
     * @param {object} detailedScores - 详细评分
     * @returns {number} 置信度
     */
    calculateConfidence(compositeScore, detailedScores) {
        // 基于综合评分的置信度
        let confidence = compositeScore;
        
        // 如果各项评分比较均衡，提升置信度
        const scores = Object.values(detailedScores);
        const avgScore = scores.reduce((sum, score) => sum + score, 0) / scores.length;
        const variance = scores.reduce((sum, score) => sum + Math.pow(score - avgScore, 2), 0) / scores.length;
        
        if (variance < 0.1) { // 评分比较均衡
            confidence *= 1.1;
        }
        
        return Math.min(1.0, confidence);
    }
    
    /**
     * @function calculateReliability - 计算可靠性等级
     * @param {object} matchResult - 匹配结果
     * @param {number} compositeScore - 综合评分
     * @returns {string} 可靠性等级
     */
    calculateReliability(matchResult, compositeScore) {
        if (compositeScore >= 0.9) return 'excellent';
        if (compositeScore >= 0.8) return 'good';
        if (compositeScore >= 0.7) return 'fair';
        if (compositeScore >= 0.6) return 'poor';
        return 'unreliable';
    }
}

/**
 * @file smart-selection.js - 智能选择服务
 * @description 实现基于订单内容的智能ID选择功能，包括车型识别和自动选择sub_category_id、car_type_id、incharge_by_backend_user_id
 * <AUTHOR> IDE
 * @created_at 2024-12-19
 */

class SmartSelectionService {
    constructor() {
        this.config = window.SYSTEM_CONFIG?.SMART_SELECTION || {};
        this.logger = window.logger || console;
        this.initialized = false;
        
        // 初始化增强组件 v3.1.0
        this.enhancedMatchingEngine = new EnhancedMatchingEngine();
        this.learningEngine = new IntelligentLearningEngine();
        this.accuracyCalculator = new DynamicAccuracyCalculator(this.learningEngine);
        
        // 初始化动态API同步管理器 (新增)
        this.apiSyncManager = new DynamicApiSyncManager();
        
        // 算法版本信息 (更新)
        this.version = '3.1.0';
        this.algorithmEnhancements = {
            fuzzyMatching: true,
            synonymSupport: true,
            learningEngine: true,
            dynamicScoring: true,
            contextAnalysis: true,
            dynamicApiSync: true,      // 新增
            pinyinMatching: true,      // 新增
            abbreviationMatch: true,   // 新增
            semanticMatching: true,    // 新增
            soundexMatching: true      // 新增
        };
        
        // 添加API同步状态监听器
        this.apiSyncManager.addStatusListener((status, data) => {
            this.handleApiSyncStatusChange(status, data);
        });
        
        // 车型识别映射表 - 基于真实API数据 (更新于2024年12月)
        this.vehicleTypeMapping = {
            // 基于乘客数量的车型映射 - 根据最新API数据更新座位数
            passengerCount: {
                1: { type: 'compact', id: 5, name: 'Compact 5 Seater', seats: 4 },
                2: { type: 'compact', id: 5, name: 'Compact 5 Seater', seats: 4 },
                3: { type: 'compact', id: 5, name: 'Compact 5 Seater', seats: 4 },
                4: { type: 'comfort', id: 6, name: 'Comfort 5 Seater', seats: 4 },
                5: { type: 'comfort', id: 6, name: 'Comfort 5 Seater', seats: 4 },
                6: { type: 'mpv', id: 16, name: 'Standard Size MPV', seats: 6 },
                7: { type: 'suv', id: 15, name: 'Mid Size SUV', seats: 7 },
                8: { type: 'mpv', id: 16, name: 'Standard Size MPV', seats: 6 },
                9: { type: 'van', id: 20, name: '10 Seater MPV / Van', seats: 9 },
                10: { type: 'van', id: 20, name: '10 Seater MPV / Van', seats: 9 },
                11: { type: 'van', id: 30, name: '12 Seater MPV', seats: 11 },
                12: { type: 'van', id: 23, name: '14 Seater Van', seats: 12 },
                13: { type: 'van', id: 23, name: '14 Seater Van', seats: 12 },
                14: { type: 'van', id: 23, name: '14 Seater Van', seats: 12 },
                15: { type: 'van', id: 24, name: '18 Seater Van', seats: 16 },
                16: { type: 'van', id: 24, name: '18 Seater Van', seats: 16 },
                17: { type: 'van', id: 24, name: '18 Seater Van', seats: 16 },
                18: { type: 'van', id: 24, name: '18 Seater Van', seats: 16 },
                19: { type: 'van', id: 24, name: '18 Seater Van', seats: 16 },
                20: { type: 'van', id: 24, name: '18 Seater Van', seats: 16 },
                25: { type: 'bus', id: 25, name: '30 Seat Mini Bus', seats: 30 },
                30: { type: 'bus', id: 25, name: '30 Seat Mini Bus', seats: 30 },
                35: { type: 'bus', id: 26, name: '44 Seater Bus', seats: 44 },
                40: { type: 'bus', id: 26, name: '44 Seater Bus', seats: 44 },
                44: { type: 'bus', id: 26, name: '44 Seater Bus', seats: 44 }
            },
            // 增强的车型关键词映射 - 支持更多车型识别和智能匹配 (更新于2024年12月)
            keywords: {
                // 经济型轿车关键词 - ID: 5, 座位数: 4
                'sedan': { id: 5, name: 'Compact 5 Seater', category: 'sedan', seats: 4 },
                'economy': { id: 5, name: 'Compact 5 Seater', category: 'sedan', seats: 4 },
                'compact': { id: 5, name: 'Compact 5 Seater', category: 'sedan', seats: 4 },
                '经济': { id: 5, name: 'Compact 5 Seater', category: 'sedan', seats: 4 },
                '轿车': { id: 5, name: 'Compact 5 Seater', category: 'sedan', seats: 4 },
                '小车': { id: 5, name: 'Compact 5 Seater', category: 'sedan', seats: 4 },
                '四座': { id: 5, name: 'Compact 5 Seater', category: 'sedan', seats: 4 },
                
                // 舒适型车辆关键词 - ID: 6, 座位数: 4
                'comfort': { id: 6, name: 'Comfort 5 Seater', category: 'comfort', seats: 4 },
                '舒适': { id: 6, name: 'Comfort 5 Seater', category: 'comfort', seats: 4 },
                '5座': { id: 6, name: 'Comfort 5 Seater', category: 'comfort', seats: 4 },
                '五座': { id: 6, name: 'Comfort 5 Seater', category: 'comfort', seats: 4 },
                
                // SUV和MPV关键词 - SUV ID: 15 (7座), MPV ID: 16 (6座)
                'suv': { id: 15, name: 'Mid Size SUV', category: 'suv', seats: 7 },
                'mpv': { id: 16, name: 'Standard Size MPV', category: 'mpv', seats: 6 },
                '商务': { id: 16, name: 'Standard Size MPV', category: 'mpv', seats: 6 },
                'business': { id: 16, name: 'Standard Size MPV', category: 'mpv', seats: 6 },
                '7座': { id: 15, name: 'Mid Size SUV', category: 'suv', seats: 7 },
                '七座': { id: 15, name: 'Mid Size SUV', category: 'suv', seats: 7 },
                '6座': { id: 16, name: 'Standard Size MPV', category: 'mpv', seats: 6 },
                '六座': { id: 16, name: 'Standard Size MPV', category: 'mpv', seats: 6 },
                
                // 豪华车型关键词 - Luxury MPV ID: 31 (6座), Alphard ID: 32 (6座)
                'luxury': { id: 31, name: 'Luxury Mpv', category: 'luxury', seats: 6 },
                'premium': { id: 31, name: 'Luxury Mpv', category: 'luxury', seats: 6 },
                '豪华': { id: 31, name: 'Luxury Mpv', category: 'luxury', seats: 6 },
                'vip': { id: 31, name: 'Luxury Mpv', category: 'luxury', seats: 6 },
                '高端': { id: 31, name: 'Luxury Mpv', category: 'luxury', seats: 6 },
                'alphard': { id: 32, name: 'Alphard/Velfire', category: 'luxury', seats: 6 },
                'velfire': { id: 32, name: 'Alphard/Velfire', category: 'luxury', seats: 6 },
                'vellfire': { id: 32, name: 'Alphard/Velfire', category: 'luxury', seats: 6 },
                
                // 大型车辆关键词 - 10座MPV ID: 20 (9座), 12座MPV ID: 30 (11座)
                'van': { id: 20, name: '10 Seater MPV / Van', category: 'van', seats: 9 },
                '面包车': { id: 20, name: '10 Seater MPV / Van', category: 'van', seats: 9 },
                '大车': { id: 20, name: '10 Seater MPV / Van', category: 'van', seats: 9 },
                '9座': { id: 20, name: '10 Seater MPV / Van', category: 'van', seats: 9 },
                '九座': { id: 20, name: '10 Seater MPV / Van', category: 'van', seats: 9 },
                '10座': { id: 20, name: '10 Seater MPV / Van', category: 'van', seats: 9 },
                '十座': { id: 20, name: '10 Seater MPV / Van', category: 'van', seats: 9 },
                '11座': { id: 30, name: '12 Seater MPV', category: 'van', seats: 11 },
                '12座': { id: 30, name: '12 Seater MPV', category: 'van', seats: 11 },
                '十二座': { id: 30, name: '12 Seater MPV', category: 'van', seats: 11 },
                '14座': { id: 23, name: '14 Seater Van', category: 'van', seats: 12 },
                '十四座': { id: 23, name: '14 Seater Van', category: 'van', seats: 12 },
                '16座': { id: 24, name: '18 Seater Van', category: 'van', seats: 16 },
                '18座': { id: 24, name: '18 Seater Van', category: 'van', seats: 16 },
                '十八座': { id: 24, name: '18 Seater Van', category: 'van', seats: 16 },
                
                // 大巴车关键词 - 30座小巴 ID: 25 (30座), 44座大巴 ID: 26 (44座)
                'bus': { id: 25, name: '30 Seat Mini Bus', category: 'bus', seats: 30 },
                'minibus': { id: 25, name: '30 Seat Mini Bus', category: 'bus', seats: 30 },
                '大巴': { id: 26, name: '44 Seater Bus', category: 'bus', seats: 44 },
                '巴士': { id: 26, name: '44 Seater Bus', category: 'bus', seats: 44 },
                '小巴': { id: 25, name: '30 Seat Mini Bus', category: 'bus', seats: 30 },
                '中巴': { id: 25, name: '30 Seat Mini Bus', category: 'bus', seats: 30 },
                '30座': { id: 25, name: '30 Seat Mini Bus', category: 'bus', seats: 30 },
                '三十座': { id: 25, name: '30 Seat Mini Bus', category: 'bus', seats: 30 },
                '44座': { id: 26, name: '44 Seater Bus', category: 'bus', seats: 44 },
                '四十四座': { id: 26, name: '44 Seater Bus', category: 'bus', seats: 44 },
                
                // 特殊服务关键词 - 在线咨询 ID: 34
                'chat': { id: 34, name: 'Please Refer Live Chat', category: 'special', seats: 1 },
                'livechat': { id: 34, name: 'Please Refer Live Chat', category: 'special', seats: 1 },
                '在线咨询': { id: 34, name: 'Please Refer Live Chat', category: 'special', seats: 1 },
                '咨询': { id: 34, name: 'Please Refer Live Chat', category: 'special', seats: 1 }
            }
        };
        
        // 服务类型映射表 - 基于最新API数据更新
        this.serviceTypeMapping = {
            // 机场服务
            '接机': { id: 7, name: 'Pickup', category: 'Airport' },
            '送机': { id: 8, name: 'Dropoff', category: 'Airport' },
            '点对点': { id: 7, name: 'Pickup', category: 'Airport' },
            '机场转乘': { id: 7, name: 'Pickup', category: 'Airport' },
            'pickup': { id: 7, name: 'Pickup', category: 'Airport' },
            'dropoff': { id: 8, name: 'Dropoff', category: 'Airport' },
            'airport_pickup': { id: 7, name: 'Pickup', category: 'Airport' },
            'airport_dropoff': { id: 8, name: 'Dropoff', category: 'Airport' },
            'point_to_point': { id: 7, name: 'Pickup', category: 'Airport' },
            'airport_transfer': { id: 7, name: 'Pickup', category: 'Airport' },
            
            // 包车服务
            '包车': { id: 9, name: 'KL to genting', category: 'Chartered' },
            'charter': { id: 9, name: 'KL to genting', category: 'Chartered' },
            'chartered': { id: 9, name: 'KL to genting', category: 'Chartered' },
            'genting': { id: 9, name: 'KL to genting', category: 'Chartered' },
            '云顶': { id: 9, name: 'KL to genting', category: 'Chartered' },
            'melaka': { id: 10, name: 'KL to melaka', category: 'Chartered' },
            '马六甲': { id: 10, name: 'KL to melaka', category: 'Chartered' },
            
            // 门票服务
            'ticket': { id: 11, name: 'Sky mirror', category: 'Ticket' },
            'sky_mirror': { id: 11, name: 'Sky mirror', category: 'Ticket' },
            '天空之镜': { id: 11, name: 'Sky mirror', category: 'Ticket' },
            'fireflies': { id: 12, name: 'Fireflies', category: 'Ticket' },
            '萤火虫': { id: 12, name: 'Fireflies', category: 'Ticket' },
            
            // 携程专用
            '携程': { id: 15, name: '携程1', category: 'Airport' },
            'ctrip': { id: 15, name: '携程1', category: 'Airport' },
            'trip.com': { id: 15, name: '携程1', category: 'Airport' },
            
            // 默认服务
            'default': { id: 16, name: 'default', category: 'default' }
        };
        
        // 后台用户映射表 - 基于最新API数据更新
        this.backendUserMapping = {
            // 超级管理员
            'super_admin': { id: 1, name: 'Super Admin', role: 'super_admin', phone: '60123456789' },
            'admin': { id: 1, name: 'Super Admin', role: 'super_admin', phone: '60123456789' },
            'superadmin': { id: 1, name: 'Super Admin', role: 'super_admin', phone: '60123456789' },
            '超级管理员': { id: 1, name: 'Super Admin', role: 'super_admin', phone: '60123456789' },
            
            // 管理员
            'manager': { id: 2, name: 'Manager', role: 'manager', phone: '60123456790' },
            'mgr': { id: 2, name: 'Manager', role: 'manager', phone: '60123456790' },
            '管理员': { id: 2, name: 'Manager', role: 'manager', phone: '60123456790' },
            
            // 操作员
            'operator': { id: 3, name: 'Operator', role: 'operator', phone: '60123456791' },
            'op': { id: 3, name: 'Operator', role: 'operator', phone: '60123456791' },
            '操作员': { id: 3, name: 'Operator', role: 'operator', phone: '60123456791' },
            
            // 客服
            'customer_service': { id: 4, name: 'Customer Service', role: 'customer_service', phone: '60123456792' },
            'cs': { id: 4, name: 'Customer Service', role: 'customer_service', phone: '60123456792' },
            '客服': { id: 4, name: 'Customer Service', role: 'customer_service', phone: '60123456792' },
            
            // 司机管理
            'driver_manager': { id: 5, name: 'Driver Manager', role: 'driver_manager', phone: '60123456793' },
            'dm': { id: 5, name: 'Driver Manager', role: 'driver_manager', phone: '60123456793' },
            '司机管理': { id: 5, name: 'Driver Manager', role: 'driver_manager', phone: '60123456793' },
            
            // 兼容性映射（保持向后兼容）
            'sub_admin': { id: 110, name: 'Sub Admin' },
            'mei_kwan': { id: 105, name: 'Mei Kwan' },
            'meikwan': { id: 108, name: 'meikwan' },
            'ringlee': { id: 206, name: 'OperatorRinglee' },
            'kcy': { id: 362, name: 'Kcy' },
            
            // 默认分配给超级管理员
            'default': { id: 1, name: 'Super Admin', role: 'super_admin', phone: '60123456789' },
            'fallback': { id: 1, name: 'Super Admin', role: 'super_admin', phone: '60123456789' },
            'gomyhire': { id: 1, name: 'Super Admin', role: 'super_admin', phone: '60123456789' },
            'chong_dealer': { id: 1, name: 'Super Admin', role: 'super_admin', phone: '60123456789' }
        };
    }
    
    /**
     * 初始化智能选择服务
     */
    async initialize() {
        try {
            this.logger.info('SmartSelection', '初始化智能选择服务');
            
            // 从应用状态获取最新的选择器数据
            await this.updateMappingFromAppState();
            
            this.initialized = true;
            this.logger.info('SmartSelection', '智能选择服务初始化完成');
            
            return true;
        } catch (error) {
            this.logger.error('SmartSelection', '初始化失败', error);
            return false;
        }
    }
    
    /**
     * 从应用状态更新映射表
     */
    async updateMappingFromAppState() {
        try {
            const app = window.app;
            if (!app || !app.appState) {
                this.logger.warn('SmartSelection', '应用状态不可用，使用默认映射');
                return;
            }
            
            // 动态车型映射系统 - 根据API返回的车型数据自动适配
            if (app.appState.carTypes && app.appState.carTypes.length > 0) {
                const vehicleMappingStrategy = this.createDynamicVehicleMapping(app.appState.carTypes);
                this.vehicleTypeMapping = vehicleMappingStrategy.mapping;
                
                this.logger.info('SmartSelection', '动态车型映射已更新', {
                    strategy: vehicleMappingStrategy.strategy,
                    totalVehicles: app.appState.carTypes.length,
                    passengerRanges: Object.keys(this.vehicleTypeMapping.passengerCount).length,
                    keywordMappings: Object.keys(this.vehicleTypeMapping.keywords).length,
                    vehicleCategories: vehicleMappingStrategy.categories
                });
            }
            
            // 动态服务类型映射系统 - 根据API返回的子分类数据自动适配
            if (app.appState.subCategories && app.appState.subCategories.length > 0) {
                const serviceMappingStrategy = this.createDynamicServiceMapping(app.appState.subCategories);
                this.serviceTypeMapping = serviceMappingStrategy.mapping;
                
                this.logger.info('SmartSelection', '动态服务类型映射已更新', {
                    strategy: serviceMappingStrategy.strategy,
                    totalServices: app.appState.subCategories.length,
                    serviceMappings: Object.keys(this.serviceTypeMapping).length,
                    serviceCategories: serviceMappingStrategy.categories,
                    defaultService: serviceMappingStrategy.defaultService
                });
            }
            
            // 动态后台用户映射系统，能够根据API返回的不同ID自动适配并选择合适的用户
            if (app.appState.backendUsers && app.appState.backendUsers.length > 0) {
                // 创建动态用户映射策略
                const userMappingStrategy = this.createDynamicUserMapping(app.appState.backendUsers);
                
                // 应用映射策略
                this.backendUserMapping = userMappingStrategy.mapping;
                
                this.logger.info('SmartSelection', '动态后台用户映射已更新', {
                    strategy: userMappingStrategy.strategy,
                    defaultUserId: userMappingStrategy.defaultUser.id,
                    defaultUserName: userMappingStrategy.defaultUser.name,
                    defaultUserRole: userMappingStrategy.defaultUser.role,
                    totalUsers: app.appState.backendUsers.length,
                    mappingKeys: Object.keys(this.backendUserMapping).length,
                    userProfiles: userMappingStrategy.userProfiles
                });
            }
            
            this.logger.debug('SmartSelection', '所有映射表更新完成', {
                vehicleTypes: Object.keys(this.vehicleTypeMapping.passengerCount).length,
                serviceTypes: Object.keys(this.serviceTypeMapping).length,
                backendUsers: Object.keys(this.backendUserMapping).length
            });
            
        } catch (error) {
            this.logger.error('SmartSelection', '更新映射表失败', error);
        }
    }
    
    /**
     * 创建动态车型映射策略 - 根据API返回的车型数据智能适配
     * @param {Array} carTypes - API返回的车型列表
     * @returns {Object} 车型映射策略对象
     */
    createDynamicVehicleMapping(carTypes) {
        try {
            // 车型分类模式识别
            const vehiclePatterns = {
                compact: /compact|小型|经济|economy/i,
                comfort: /comfort|舒适|standard/i,
                luxury: /luxury|豪华|premium|alphard|velfire|vellfire/i,
                suv: /suv|越野|sport.*utility/i,
                mpv: /mpv|multi.*purpose|商务/i,
                van: /van|面包|客车/i,
                bus: /bus|巴士|大巴|coach/i,
                minibus: /mini.*bus|中巴|小巴/i
            };
            
            // 座位数范围定义
            const seatRanges = {
                micro: { min: 1, max: 2, category: 'micro' },
                compact: { min: 3, max: 5, category: 'compact' },
                standard: { min: 6, max: 8, category: 'standard' },
                large: { min: 9, max: 15, category: 'large' },
                xlarge: { min: 16, max: 30, category: 'xlarge' },
                bus: { min: 31, max: 100, category: 'bus' }
            };
            
            // 按优先级排序车型
            const sortedCarTypes = carTypes.sort((a, b) => (a.priority || 999) - (b.priority || 999));
            
            // 分析车型配置文件
            const vehicleProfiles = sortedCarTypes.map(carType => {
                const profile = {
                    id: carType.id,
                    type: carType.type || `Vehicle_${carType.id}`,
                    seatNumber: carType.seat_number || 4,
                    priority: carType.priority || 999,
                    category: 'other',
                    keywords: [],
                    suitableFor: []
                };
                
                // 识别车型类别
                const typeName = profile.type.toLowerCase();
                for (const [category, pattern] of Object.entries(vehiclePatterns)) {
                    if (pattern.test(typeName)) {
                        profile.category = category;
                        break;
                    }
                }
                
                // 根据座位数确定适用范围
                for (const [range, config] of Object.entries(seatRanges)) {
                    if (profile.seatNumber >= config.min && profile.seatNumber <= config.max) {
                        profile.suitableFor.push(range);
                    }
                }
                
                // 生成关键词
                profile.keywords = [
                    profile.category,
                    typeName.replace(/\s+/g, '_'),
                    `${profile.seatNumber}座`,
                    `${profile.seatNumber}_seater`,
                    profile.seatNumber.toString()
                ];
                
                // 添加特殊关键词
                if (profile.category === 'luxury') {
                    profile.keywords.push('vip', '豪华', 'premium');
                }
                if (profile.category === 'compact') {
                    profile.keywords.push('经济', 'economy', 'budget');
                }
                
                return profile;
            });
            
            // 构建动态映射表
            const dynamicMapping = {
                passengerCount: {},
                keywords: {},
                categories: {},
                seatRanges: {}
            };
            
            // 为每个乘客数量范围分配最优车型
            for (let passengerCount = 1; passengerCount <= 100; passengerCount++) {
                let bestVehicle = null;
                let bestScore = -1;
                
                for (const profile of vehicleProfiles) {
                    // 计算适配分数
                    let score = 0;
                    
                    // 座位数适配性 (最重要)
                    if (profile.seatNumber >= passengerCount) {
                        score += 100 - (profile.seatNumber - passengerCount) * 2; // 座位数越接近越好
                    } else {
                        continue; // 座位数不足，跳过
                    }
                    
                    // 优先级加分 (优先级越低数字越小，分数越高)
                    score += (1000 - profile.priority) / 10;
                    
                    // 类别适配性加分
                    if (passengerCount <= 2 && profile.category === 'compact') score += 20;
                    else if (passengerCount <= 5 && profile.category === 'comfort') score += 15;
                    else if (passengerCount <= 8 && (profile.category === 'mpv' || profile.category === 'suv')) score += 15;
                    else if (passengerCount > 8 && (profile.category === 'van' || profile.category === 'bus')) score += 20;
                    
                    if (score > bestScore) {
                        bestScore = score;
                        bestVehicle = profile;
                    }
                }
                
                if (bestVehicle) {
                    dynamicMapping.passengerCount[passengerCount] = {
                        type: bestVehicle.category,
                        id: bestVehicle.id,
                        name: bestVehicle.type,
                        priority: bestVehicle.priority,
                        seatNumber: bestVehicle.seatNumber,
                        score: bestScore
                    };
                }
            }
            
            // 构建关键词映射
            vehicleProfiles.forEach(profile => {
                profile.keywords.forEach(keyword => {
                    if (!dynamicMapping.keywords[keyword] || 
                        dynamicMapping.keywords[keyword].priority > profile.priority) {
                        dynamicMapping.keywords[keyword] = {
                            id: profile.id,
                            name: profile.type,
                            priority: profile.priority,
                            category: profile.category
                        };
                    }
                });
                
                // 类别映射
                if (!dynamicMapping.categories[profile.category] || 
                    dynamicMapping.categories[profile.category].priority > profile.priority) {
                    dynamicMapping.categories[profile.category] = {
                        id: profile.id,
                        name: profile.type,
                        priority: profile.priority
                    };
                }
            });
            
            return {
                strategy: 'dynamic_vehicle_adaptation',
                mapping: dynamicMapping,
                categories: Object.keys(dynamicMapping.categories),
                vehicleProfiles: vehicleProfiles.map(p => ({
                    id: p.id,
                    name: p.type,
                    category: p.category,
                    seats: p.seatNumber,
                    priority: p.priority
                }))
            };
            
        } catch (error) {
            this.logger.error('SmartSelection', '创建动态车型映射失败', error);
            return {
                strategy: 'fallback',
                mapping: this.vehicleTypeMapping,
                categories: ['compact', 'comfort', 'mpv', 'van'],
                vehicleProfiles: []
            };
        }
    }
    
    /**
     * 创建动态服务类型映射策略 - 根据API返回的子分类数据智能适配
     * @param {Array} subCategories - API返回的子分类列表
     * @returns {Object} 服务类型映射策略对象
     */
    createDynamicServiceMapping(subCategories) {
        try {
            // 服务类型模式识别
            const servicePatterns = {
                airport_pickup: /pickup|接机|airport.*pick|机场.*接/i,
                airport_dropoff: /drop.*off|送机|airport.*drop|机场.*送/i,
                charter: /charter|包车|chartered|租车/i,
                tour: /tour|旅游|观光|sightseeing/i,
                transfer: /transfer|转乘|point.*to.*point/i,
                ticket: /ticket|景点|景点|attraction/i,
                hotel: /hotel|酒店|住宿|accommodation/i,
                business: /business|商务|corporate/i,
                wedding: /wedding|婚礼|ceremony/i,
                event: /event|活动|conference|会议/i,
                emergency: /emergency|紧急|urgent/i,
                vip: /vip|premium|豪华|luxury/i
            };
            
            // 主分类优先级
            const categoryPriority = {
                'Airport': 1,
                'Chartered': 2,
                'Charter': 2,
                'Transfer': 3,
                'Tour': 4,
                'Ticket': 5,
                'Hotel': 6,
                'Business': 7,
                'Event': 8,
                'default': 999
            };
            
            // 分析服务配置文件
            const serviceProfiles = subCategories.map(category => {
                const profile = {
                    id: category.id,
                    name: category.name || `Service_${category.id}`,
                    mainCategory: category.main_category || 'default',
                    serviceType: 'other',
                    keywords: [],
                    priority: categoryPriority[category.main_category] || 999,
                    presetData: category.preset_data || {},
                    requiredFields: category.required_fields || []
                };
                
                // 识别服务类型
                const serviceName = profile.name.toLowerCase();
                const mainCategoryName = profile.mainCategory.toLowerCase();
                
                for (const [type, pattern] of Object.entries(servicePatterns)) {
                    if (pattern.test(serviceName) || pattern.test(mainCategoryName)) {
                        profile.serviceType = type;
                        break;
                    }
                }
                
                // 生成关键词
                profile.keywords = [
                    profile.serviceType,
                    serviceName.replace(/\s+/g, '_'),
                    mainCategoryName.replace(/\s+/g, '_'),
                    profile.mainCategory.toLowerCase(),
                    profile.name.toLowerCase()
                ];
                
                // 添加中文关键词
                if (profile.serviceType === 'airport_pickup') {
                    profile.keywords.push('接机', 'pickup', 'airport_pickup');
                } else if (profile.serviceType === 'airport_dropoff') {
                    profile.keywords.push('送机', 'dropoff', 'airport_dropoff');
                } else if (profile.serviceType === 'charter') {
                    profile.keywords.push('包车', 'charter', 'chartered');
                }
                
                // 根据预设数据添加特殊关键词
                if (profile.presetData.order_type) {
                    profile.keywords.push(profile.presetData.order_type);
                }
                
                return profile;
            });
            
            // 按优先级排序
            const sortedProfiles = serviceProfiles.sort((a, b) => a.priority - b.priority);
            
            // 构建动态映射表
            const dynamicMapping = {};
            
            // 为每个关键词建立映射
            sortedProfiles.forEach(profile => {
                profile.keywords.forEach(keyword => {
                    if (!dynamicMapping[keyword] || 
                        dynamicMapping[keyword].priority > profile.priority) {
                        dynamicMapping[keyword] = {
                            id: profile.id,
                            name: profile.name,
                            category: profile.mainCategory,
                            priority: profile.priority,
                            serviceType: profile.serviceType
                        };
                    }
                });
            });
            
            // 添加通用映射
            const commonMappings = {
                '点对点': 'airport_pickup',
                '机场转乘': 'airport_pickup',
                'point_to_point': 'airport_pickup',
                'airport_transfer': 'airport_pickup',
                '旅游': 'tour',
                '观光': 'tour',
                '景点': 'ticket',
                '景点': 'ticket'
            };
            
            Object.entries(commonMappings).forEach(([keyword, serviceType]) => {
                const matchingProfile = sortedProfiles.find(p => p.serviceType === serviceType);
                if (matchingProfile && !dynamicMapping[keyword]) {
                    dynamicMapping[keyword] = {
                        id: matchingProfile.id,
                        name: matchingProfile.name,
                        category: matchingProfile.mainCategory,
                        priority: matchingProfile.priority,
                        serviceType: matchingProfile.serviceType
                    };
                }
            });
            
            // 确定默认服务
            const defaultService = sortedProfiles[0] || {
                id: 16,
                name: 'default',
                mainCategory: 'default',
                priority: 999
            };
            
            return {
                strategy: 'dynamic_service_adaptation',
                mapping: dynamicMapping,
                categories: [...new Set(serviceProfiles.map(p => p.serviceType))],
                defaultService: {
                    id: defaultService.id,
                    name: defaultService.name,
                    category: defaultService.mainCategory
                },
                serviceProfiles: serviceProfiles.map(p => ({
                    id: p.id,
                    name: p.name,
                    category: p.mainCategory,
                    serviceType: p.serviceType,
                    priority: p.priority
                }))
            };
            
        } catch (error) {
            this.logger.error('SmartSelection', '创建动态服务类型映射失败', error);
            return {
                strategy: 'fallback',
                mapping: this.serviceTypeMapping,
                categories: ['airport_pickup', 'airport_dropoff', 'charter'],
                defaultService: { id: 16, name: 'default', category: 'default' },
                serviceProfiles: []
            };
        }
    }
    
    /**
     * 创建动态后台用户映射策略 - 简化版：选择第一个用户
     * @param {Array} backendUsers - API返回的后台用户列表
     * @returns {Object} 用户映射策略对象
     */
    createDynamicUserMapping(backendUsers) {
        try {
            // 简化逻辑：直接选择第一个用户
            if (!backendUsers || backendUsers.length === 0) {
                this.logger.warn('SmartSelection', '后台用户列表为空，使用默认用户');
                return {
                    strategy: 'fallback',
                    defaultUser: { id: 1, name: 'Super Admin', role: 'super_admin' },
                    mapping: {
                        'default': { id: 1, name: 'Super Admin', role: 'super_admin' },
                        'fallback': { id: 1, name: 'Super Admin', role: 'super_admin' },
                        'gomyhire': { id: 1, name: 'Super Admin', role: 'super_admin' },
                        'chong_dealer': { id: 1, name: 'Super Admin', role: 'super_admin' }
                    },
                    userProfiles: [{ id: 1, name: 'Super Admin', role: 'super_admin' }]
                };
            }

            // 选择第一个用户作为默认用户
            const firstUser = backendUsers[0];
            const defaultUser = {
                id: firstUser.id,
                name: firstUser.name || firstUser.username || `User_${firstUser.id}`,
                role: firstUser.role || 'Operator'
            };

            this.logger.info('SmartSelection', '选择第一个后台用户', {
                userId: defaultUser.id,
                userName: defaultUser.name,
                userRole: defaultUser.role,
                totalUsers: backendUsers.length
            });

            // 创建简化的映射表 - 所有映射都指向第一个用户
            const mapping = {
                // 默认映射
                'default': { id: defaultUser.id, name: defaultUser.name, role: defaultUser.role },
                'fallback': { id: defaultUser.id, name: defaultUser.name, role: defaultUser.role },
                'gomyhire': { id: defaultUser.id, name: defaultUser.name, role: defaultUser.role },
                'chong_dealer': { id: defaultUser.id, name: defaultUser.name, role: defaultUser.role },
                
                // 兼容性映射（保持向后兼容）
                'super_admin': { id: defaultUser.id, name: defaultUser.name, role: defaultUser.role },
                'admin': { id: defaultUser.id, name: defaultUser.name, role: defaultUser.role },
                'manager': { id: defaultUser.id, name: defaultUser.name, role: defaultUser.role },
                'operator': { id: defaultUser.id, name: defaultUser.name, role: defaultUser.role },
                'customer_service': { id: defaultUser.id, name: defaultUser.name, role: defaultUser.role },
                'driver_manager': { id: defaultUser.id, name: defaultUser.name, role: defaultUser.role }
            };

            // 为第一个用户创建基本映射
            const cleanName = defaultUser.name.toLowerCase().replace(/\s+/g, '_').replace(/[^a-z0-9_]/g, '');
            mapping[cleanName] = { id: defaultUser.id, name: defaultUser.name, role: defaultUser.role };
            mapping[`id_${defaultUser.id}`] = { id: defaultUser.id, name: defaultUser.name, role: defaultUser.role };

            return {
                strategy: 'first_user_simplified',
                defaultUser,
                mapping,
                userProfiles: [{
                    id: defaultUser.id,
                    name: defaultUser.name,
                    role: defaultUser.role,
                    priority: 1,
                    capabilities: ['all'],
                    specialties: []
                }]
            };

        } catch (error) {
            this.logger.error('SmartSelection', '创建动态用户映射失败', error);
            
            // 返回安全的默认映射
            const firstUser = backendUsers && backendUsers.length > 0 ? backendUsers[0] : { id: 1, name: 'Super Admin', role: 'super_admin' };
            return {
                strategy: 'fallback',
                defaultUser: firstUser,
                mapping: {
                    'default': { id: firstUser.id, name: firstUser.name, role: firstUser.role || 'Operator' },
                    'fallback': { id: firstUser.id, name: firstUser.name, role: firstUser.role || 'Operator' },
                    'gomyhire': { id: firstUser.id, name: firstUser.name, role: firstUser.role || 'Operator' },
                    'chong_dealer': { id: firstUser.id, name: firstUser.name, role: firstUser.role || 'Operator' }
                },
                userProfiles: [{ id: firstUser.id, name: firstUser.name, role: firstUser.role || 'Operator' }]
            };
        }
    }

    /**
     * 应用智能选择 - 核心方法
     * @param {Object} orderData - 订单数据
     * @param {string} source - 调用源 ('manual', 'llm', 'api')
     * @returns {Object} 增强后的订单数据
     */
    applySmartSelection(orderData, source = 'manual') {
        try {
            if (!this.initialized) {
                this.logger.warn('SmartSelection', '服务未初始化，使用默认值');
            }

            const startTime = Date.now();
            const enhancedData = { ...orderData };
            const selectionResults = {
                source,
                timestamp: new Date().toISOString(),
                results: {},
                confidence: {},
                fallbacks: {},
                processingTime: 0
            };

            // 1. 智能车型选择
            const vehicleSelection = this.selectVehicleType(orderData);
            if (vehicleSelection.success) {
                enhancedData.car_type_id = vehicleSelection.carTypeId;
                selectionResults.results.vehicle = {
                    id: vehicleSelection.carTypeId,
                    name: vehicleSelection.carTypeName,
                    reason: vehicleSelection.reason,
                    method: vehicleSelection.method
                };
                selectionResults.confidence.vehicle = vehicleSelection.confidence;
                
                this.logger.info('SmartSelection', '车型选择完成', {
                    carTypeId: vehicleSelection.carTypeId,
                    carTypeName: vehicleSelection.carTypeName,
                    method: vehicleSelection.method,
                    confidence: vehicleSelection.confidence
                });
            } else {
                selectionResults.fallbacks.vehicle = vehicleSelection.error;
                this.logger.warn('SmartSelection', '车型选择失败，使用默认值', vehicleSelection.error);
            }

            // 2. 智能服务类型选择
            const serviceSelection = this.selectServiceType(orderData);
            if (serviceSelection.success) {
                enhancedData.sub_category_id = serviceSelection.subCategoryId;
                selectionResults.results.service = {
                    id: serviceSelection.subCategoryId,
                    name: serviceSelection.subCategoryName,
                    reason: serviceSelection.reason,
                    method: serviceSelection.method
                };
                selectionResults.confidence.service = serviceSelection.confidence;
                
                this.logger.info('SmartSelection', '服务类型选择完成', {
                    subCategoryId: serviceSelection.subCategoryId,
                    subCategoryName: serviceSelection.subCategoryName,
                    method: serviceSelection.method,
                    confidence: serviceSelection.confidence
                });
            } else {
                selectionResults.fallbacks.service = serviceSelection.error;
                this.logger.warn('SmartSelection', '服务类型选择失败，使用默认值', serviceSelection.error);
            }

            // 3. 智能后台用户选择
            const userSelection = this.selectBackendUser(orderData);
            if (userSelection.success) {
                enhancedData.incharge_by_backend_user_id = userSelection.userId;
                selectionResults.results.user = {
                    id: userSelection.userId,
                    name: userSelection.userName,
                    reason: userSelection.reason,
                    method: userSelection.method
                };
                selectionResults.confidence.user = userSelection.confidence;
                
                this.logger.info('SmartSelection', '后台用户选择完成', {
                    userId: userSelection.userId,
                    userName: userSelection.userName,
                    method: userSelection.method,
                    confidence: userSelection.confidence
                });
            } else {
                selectionResults.fallbacks.user = userSelection.error;
                this.logger.warn('SmartSelection', '后台用户选择失败，使用默认值', userSelection.error);
            }

            // 计算处理时间
            selectionResults.processingTime = Date.now() - startTime;

            // 添加智能选择元数据
            enhancedData._smartSelection = selectionResults;

            this.logger.info('SmartSelection', '智能选择完成', {
                source,
                processingTime: selectionResults.processingTime,
                successfulSelections: Object.keys(selectionResults.results).length,
                fallbacks: Object.keys(selectionResults.fallbacks).length
            });

            return enhancedData;

        } catch (error) {
            this.logger.error('SmartSelection', '智能选择失败', error);
            return {
                ...orderData,
                _smartSelection: {
                    source,
                    timestamp: new Date().toISOString(),
                    error: error.message,
                    fallback: true
                }
            };
        }
    }

    /**
     * @function selectVehicleType - 增强车型选择算法 v3.0
     * @param {Object} orderData - 订单数据
     * @returns {Object} 车型选择结果，包含增强的精度评分
     */
    selectVehicleType(orderData) {
        try {
            const startTime = performance.now();
            const passengerCount = parseInt(orderData.passenger_count) || 1;
            const carType = orderData.car_type || '';
            const serviceType = orderData.service_type || '';
            const remarks = orderData.remarks || '';
            
            // 构建搜索上下文
            const searchContext = {
                text: `${carType} ${serviceType} ${remarks}`.toLowerCase(),
                orderData: orderData,
                timestamp: Date.now()
            };

            // 候选方案集合
            const candidates = [];

            // 方法1: 基于乘客数量的精确匹配 (增强版)
            if (this.vehicleTypeMapping.passengerCount[passengerCount]) {
                const vehicle = this.vehicleTypeMapping.passengerCount[passengerCount];
                const exactMatchResult = {
                    success: true,
                    carTypeId: vehicle.id,
                    carTypeName: vehicle.name,
                    method: 'passenger_count_exact',
                    reason: `基于乘客数量 ${passengerCount} 的精确匹配`,
                    confidence: 0.95,
                    seats: vehicle.seats,
                    category: vehicle.type
                };
                
                // 使用动态评分系统
                const enhancedScore = this.accuracyCalculator.calculateCompositeScore(
                    exactMatchResult, searchContext);
                candidates.push({
                    ...exactMatchResult,
                    enhancedScore: enhancedScore,
                    finalConfidence: enhancedScore.confidence
                });
            }

            // 方法2: 增强关键词匹配算法
            let bestKeywordMatch = null;
            let maxKeywordScore = 0;
            
            for (const [keyword, vehicle] of Object.entries(this.vehicleTypeMapping.keywords)) {
                const matchResult = this.enhancedMatchingEngine.enhancedKeywordMatch(
                    searchContext.text, keyword);
                
                if (matchResult.matched && matchResult.score > maxKeywordScore) {
                    // 座位数适配性检查
                    const seatFit = vehicle.seats >= passengerCount;
                    const seatPenalty = seatFit ? 0 : 0.2;
                    
                    const keywordMatchResult = {
                        success: true,
                        carTypeId: vehicle.id,
                        carTypeName: vehicle.name,
                        method: 'enhanced_keyword_match',
                        reason: `增强关键词匹配: "${matchResult.matchedTerm}" (${matchResult.method})`,
                        confidence: Math.max(0.1, matchResult.score - seatPenalty),
                        seats: vehicle.seats,
                        category: vehicle.category,
                        matchDetails: matchResult,
                        seatFit: seatFit
                    };
                    
                    const enhancedScore = this.accuracyCalculator.calculateCompositeScore(
                        keywordMatchResult, searchContext);
                    
                    bestKeywordMatch = {
                        ...keywordMatchResult,
                        enhancedScore: enhancedScore,
                        finalConfidence: enhancedScore.confidence
                    };
                    maxKeywordScore = matchResult.score;
                }
            }
            
            if (bestKeywordMatch) {
                candidates.push(bestKeywordMatch);
            }

            // 方法3: 智能范围匹配 (优化版)
            const rangeCandidates = [];
            for (const [count, vehicle] of Object.entries(this.vehicleTypeMapping.passengerCount)) {
                const seatCount = parseInt(count);
                if (seatCount >= passengerCount) {
                    const difference = seatCount - passengerCount;
                    const proximityScore = Math.max(0.3, 1 - (difference / 10)); // 距离越近分数越高
                    
                    const rangeMatchResult = {
                        success: true,
                        carTypeId: vehicle.id,
                        carTypeName: vehicle.name,
                        method: 'smart_range_match',
                        reason: `智能范围匹配: ${passengerCount}人 → ${seatCount}座 (差值: ${difference})`,
                        confidence: proximityScore,
                        seats: vehicle.seats,
                        category: vehicle.type,
                        passengerFit: seatCount >= passengerCount
                    };
                    
                    const enhancedScore = this.accuracyCalculator.calculateCompositeScore(
                        rangeMatchResult, searchContext);
                    
                    rangeCandidates.push({
                        ...rangeMatchResult,
                        enhancedScore: enhancedScore,
                        finalConfidence: enhancedScore.confidence,
                        difference: difference
                    });
                }
            }
            
            // 选择最优的范围匹配结果
            if (rangeCandidates.length > 0) {
                const bestRangeMatch = rangeCandidates.sort((a, b) => 
                    b.finalConfidence - a.finalConfidence)[0];
                candidates.push(bestRangeMatch);
            }

            // 方法4: 上下文感知匹配
            const contextualResult = this.performContextualMatching(orderData, searchContext);
            if (contextualResult.success) {
                const enhancedScore = this.accuracyCalculator.calculateCompositeScore(
                    contextualResult, searchContext);
                candidates.push({
                    ...contextualResult,
                    enhancedScore: enhancedScore,
                    finalConfidence: enhancedScore.confidence
                });
            }

            // 候选方案评估和排序
            if (candidates.length > 0) {
                // 按最终置信度排序
                candidates.sort((a, b) => b.finalConfidence - a.finalConfidence);
                
                // 学习引擎模式分析
                const patterns = this.learningEngine.analyzePatterns(orderData);
                
                // 如果有历史推荐，进行权重调整
                if (patterns.suggestedMethod) {
                    const suggestedCandidate = candidates.find(c => c.method === patterns.suggestedMethod);
                    if (suggestedCandidate) {
                        suggestedCandidate.finalConfidence *= 1.1; // 10%提升
                        suggestedCandidate.reason += ` [历史推荐: ${patterns.reasonCode}]`;
                        // 重新排序
                        candidates.sort((a, b) => b.finalConfidence - a.finalConfidence);
                    }
                }
                
                const bestCandidate = candidates[0];
                const processingTime = performance.now() - startTime;
                
                // 记录选择结果到学习引擎
                const selectionRecord = {
                    orderData: orderData,
                    result: bestCandidate,
                    method: bestCandidate.method,
                    confidence: bestCandidate.finalConfidence,
                    processingTime: processingTime,
                    candidateCount: candidates.length,
                    success: true
                };
                
                this.learningEngine.recordSelection(selectionRecord);
                
                // 返回增强结果
                return {
                    ...bestCandidate,
                    algorithmVersion: this.version,
                    processingTime: processingTime,
                    candidateCount: candidates.length,
                    allCandidates: candidates.slice(0, 3), // 返回前3个候选方案
                    learningInsights: patterns,
                    enhancedFeatures: this.algorithmEnhancements
                };
            }

            // 回退方案: 智能默认选择
            const fallbackResult = this.getIntelligentFallback(orderData, searchContext);
            const processingTime = performance.now() - startTime;
            
            // 记录回退情况
            const fallbackRecord = {
                orderData: orderData,
                result: fallbackResult,
                method: 'intelligent_fallback',
                confidence: fallbackResult.confidence,
                processingTime: processingTime,
                success: false,
                reason: 'no_suitable_candidates_found'
            };
            
            this.learningEngine.recordSelection(fallbackRecord);
            
            return {
                ...fallbackResult,
                algorithmVersion: this.version,
                processingTime: processingTime,
                enhancedFeatures: this.algorithmEnhancements
            };

        } catch (error) {
            // 错误处理和记录
            const errorRecord = {
                orderData: orderData,
                error: error.message,
                method: 'error_handling',
                success: false,
                timestamp: Date.now()
            };
            
            this.learningEngine.recordSelection(errorRecord);
            
            return {
                success: false,
                error: error.message,
                method: 'error_fallback',
                algorithmVersion: this.version,
                enhancedFeatures: this.algorithmEnhancements
            };
        }
    }
    
    /**
     * @function performContextualMatching - 执行上下文感知匹配
     * @param {Object} orderData - 订单数据
     * @param {Object} searchContext - 搜索上下文
     * @returns {Object} 上下文匹配结果
     */
    performContextualMatching(orderData, searchContext) {
        const serviceType = (orderData.service_type || '').toLowerCase();
        const ota = (orderData.ota || '').toLowerCase();
        const passengers = parseInt(orderData.passenger_count) || 1;
        
        // 服务类型上下文规则
        const contextRules = {
            // 豪华服务上下文
            luxury: {
                keywords: ['luxury', 'vip', 'premium', '豪华', '高端'],
                preferredVehicles: [31, 32], // Luxury MPV, Alphard
                confidence: 0.85
            },
            // 商务服务上下文
            business: {
                keywords: ['business', 'corporate', '商务', '企业'],
                preferredVehicles: [16, 31], // Standard MPV, Luxury MPV
                confidence: 0.8
            },
            // 大团体上下文
            largeGroup: {
                condition: passengers > 6,
                preferredVehicles: [20, 23, 24], // 10座, 14座, 18座
                confidence: 0.9
            },
            // 机场服务上下文
            airport: {
                keywords: ['airport', 'pickup', 'dropoff', '机场', '接机', '送机'],
                preferredVehicles: [5, 6, 16], // 经济型或舒适型
                confidence: 0.75
            }
        };
        
        // 评估每个上下文规则
        for (const [ruleName, rule] of Object.entries(contextRules)) {
            let matched = false;
            
            if (rule.keywords) {
                matched = rule.keywords.some(keyword => 
                    searchContext.text.includes(keyword.toLowerCase()));
            }
            
            if (rule.condition) {
                matched = rule.condition;
            }
            
            if (matched && rule.preferredVehicles) {
                // 在偏好车型中选择最适合的
                for (const vehicleId of rule.preferredVehicles) {
                    const vehicle = this.findVehicleById(vehicleId);
                    if (vehicle && (!vehicle.seats || vehicle.seats >= passengers)) {
                        return {
                            success: true,
                            carTypeId: vehicleId,
                            carTypeName: vehicle.name,
                            method: 'contextual_analysis',
                            reason: `上下文感知匹配: ${ruleName}规则`,
                            confidence: rule.confidence,
                            seats: vehicle.seats,
                            category: vehicle.category || 'contextual',
                            contextRule: ruleName
                        };
                    }
                }
            }
        }
        
        return { success: false };
    }
    
    /**
     * @function findVehicleById - 根据ID查找车型信息
     * @param {number} vehicleId - 车型ID
     * @returns {Object|null} 车型信息
     */
    findVehicleById(vehicleId) {
        // 在乘客数量映射中查找
        for (const vehicle of Object.values(this.vehicleTypeMapping.passengerCount)) {
            if (vehicle.id === vehicleId) {
                return vehicle;
            }
        }
        
        // 在关键词映射中查找
        for (const vehicle of Object.values(this.vehicleTypeMapping.keywords)) {
            if (vehicle.id === vehicleId) {
                return vehicle;
            }
        }
        
        return null;
    }
    
    /**
     * @function getIntelligentFallback - 获取智能回退方案
     * @param {Object} orderData - 订单数据
     * @param {Object} searchContext - 搜索上下文
     * @returns {Object} 回退方案
     */
    getIntelligentFallback(orderData, searchContext) {
        const passengers = parseInt(orderData.passenger_count) || 1;
        
        // 基于乘客数量的智能回退
        if (passengers <= 4) {
            return {
                success: true,
                carTypeId: 6,
                carTypeName: 'Comfort 5 Seater',
                method: 'intelligent_fallback',
                reason: `智能回退: ${passengers}人适合舒适型5座车`,
                confidence: 0.6,
                seats: 4,
                category: 'fallback'
            };
        } else if (passengers <= 7) {
            return {
                success: true,
                carTypeId: 15,
                carTypeName: 'Mid Size SUV',
                method: 'intelligent_fallback',
                reason: `智能回退: ${passengers}人适合7座SUV`,
                confidence: 0.65,
                seats: 7,
                category: 'fallback'
            };
        } else {
            return {
                success: true,
                carTypeId: 20,
                carTypeName: '10 Seater MPV / Van',
                method: 'intelligent_fallback',
                reason: `智能回退: ${passengers}人适合大型MPV`,
                confidence: 0.7,
                seats: 9,
                category: 'fallback'
            };
        }
    }

    /**
     * 选择服务类型 - 新的简化逻辑
     * @param {Object} orderData - 订单数据
     * @returns {Object} 服务类型选择结果
     */
    selectServiceType(orderData) {
        try {
            // 第一步：基础服务类型判断（LLM已处理）
            const baseServiceType = this.determineBaseServiceType(orderData);
            
            // 第二步：根据基础类型映射到具体API服务类型ID
            const mappedService = this.mapToApiServiceType(baseServiceType, orderData);
            
            return {
                success: true,
                subCategoryId: mappedService.id,
                subCategoryName: mappedService.name,
                baseType: baseServiceType,
                method: 'simplified_mapping',
                reason: mappedService.reason,
                confidence: mappedService.confidence
            };

        } catch (error) {
            return {
                success: false,
                error: error.message,
                method: 'error_fallback'
            };
        }
    }

    /**
     * 确定基础服务类型（pickup/dropoff/charter）
     * @param {Object} orderData - 订单数据
     * @returns {string} 基础服务类型
     */
    determineBaseServiceType(orderData) {
        const serviceType = (orderData.service_type || '').toLowerCase();
        const orderType = (orderData.order_type || '').toLowerCase();
        const pickupLocation = (orderData.pickup_location || '').toLowerCase();
        const dropoffLocation = (orderData.dropoff_location || '').toLowerCase();
        const flightNumber = orderData.flight_number || '';
        const remarks = (orderData.remarks || '').toLowerCase();

        // 构建搜索文本
         const searchText = `${serviceType} ${orderType} ${pickupLocation} ${dropoffLocation} ${remarks}`.toLowerCase();

         // 1. 优先使用LLM已判断的service_type
         if (serviceType) {
             if (serviceType.includes('pickup') || serviceType === 'pickup') {
                 return 'pickup';
             }
             if (serviceType.includes('dropoff') || serviceType === 'dropoff') {
                 return 'dropoff';
             }
             if (serviceType.includes('charter') || serviceType === 'charter') {
                 return 'charter';
             }
         }

         // 2. 基于关键词的智能判断
         const pickupKeywords = ['接机', 'pickup', 'airport.*pick', '机场.*接'];
         const dropoffKeywords = ['送机', 'dropoff', 'airport.*drop', '机场.*送'];
         const charterKeywords = ['包车', 'charter', '一日游', '点对点', '云顶', 'genting', '马六甲', 'melaka'];

         // 检查接机关键词
         for (const keyword of pickupKeywords) {
             if (new RegExp(keyword, 'i').test(searchText)) {
                 return 'pickup';
             }
         }

         // 检查送机关键词
         for (const keyword of dropoffKeywords) {
             if (new RegExp(keyword, 'i').test(searchText)) {
                 return 'dropoff';
             }
         }

         // 检查包车关键词
         for (const keyword of charterKeywords) {
             if (new RegExp(keyword, 'i').test(searchText)) {
                 return 'charter';
             }
         }

         // 3. 基于位置信息判断
         const airportKeywords = ['airport', 'klia', 'klia2', 'lcct', 'subang', '机场', '航站楼'];
         const isPickupFromAirport = airportKeywords.some(keyword => 
             pickupLocation.includes(keyword.toLowerCase())
         );
         const isDropoffToAirport = airportKeywords.some(keyword => 
             dropoffLocation.includes(keyword.toLowerCase())
         );

         if (isPickupFromAirport && !isDropoffToAirport) {
             return 'pickup';
         }
         if (!isPickupFromAirport && isDropoffToAirport) {
             return 'dropoff';
         }

         // 4. 基于航班信息判断
         if (flightNumber && flightNumber.trim()) {
             // 有航班号通常是接机服务
             return 'pickup';
         }

         // 5. 默认返回pickup
         return 'pickup';
     }

     /**
      * 根据基础服务类型映射到具体API服务类型ID
      * @param {string} baseType - 基础服务类型 (pickup/dropoff/charter)
      * @param {Object} orderData - 订单数据
      * @returns {Object} 映射结果
      */
     mapToApiServiceType(baseType, orderData) {
         switch (baseType) {
             case 'pickup':
                 return {
                     id: 7,
                     name: 'Pickup',
                     reason: '接机服务',
                     confidence: 0.9
                 };

             case 'dropoff':
                 return {
                     id: 8,
                     name: 'Dropoff',
                     reason: '送机服务',
                     confidence: 0.9
                 };

             case 'charter':
                 return {
                     id: 43,
                     name: 'Charter',
                     reason: '包车服务',
                     confidence: 0.9
                 };

             default:
                 // 默认返回接机服务
                 return {
                     id: 7,
                     name: 'Pickup',
                     reason: '默认接机服务',
                     confidence: 0.5
                 };
         }
     }

     // 保留原有的复杂逻辑作为备用（已废弃）
     selectServiceTypeOld(orderData) {
         try {
             const serviceType = orderData.service_type || '';
             const orderType = orderData.order_type || '';
             const pickupLocation = orderData.pickup_location || '';
             const dropoffLocation = orderData.dropoff_location || '';
             const flightNumber = orderData.flight_number || '';
             const remarks = orderData.remarks || '';
             const ota = orderData.ota || '';

             // 构建搜索文本
             const searchText = `${serviceType} ${orderType} ${pickupLocation} ${dropoffLocation} ${remarks} ${ota}`.toLowerCase();

             // 方法1: OTA平台优先判断（特殊渠道优先处理）
            if (ota) {
                const otaLower = ota.toLowerCase();
                if (otaLower.includes('ctrip') || otaLower.includes('携程') || otaLower.includes('trip.com')) {
                    const ctripService = this.serviceTypeMapping['携程'] || { id: 15, name: '携程1' };
                    return {
                        success: true,
                        subCategoryId: ctripService.id,
                        subCategoryName: ctripService.name,
                        method: 'ota_priority',
                        reason: `基于OTA平台 "${ota}" 优先匹配`,
                        confidence: 0.95
                    };
                }
            }

            // 方法2: 精确关键词匹配（按优先级排序）
            const priorityKeywords = [
                // 高优先级关键词
                'sky_mirror', '天空之镜', 'fireflies', '萤火虫',
                'genting', '云顶', 'melaka', '马六甲',
                'pickup', '接机', 'dropoff', '送机',
                'airport_pickup', 'airport_dropoff', 'charter', '包车'
            ];

            for (const keyword of priorityKeywords) {
                if (searchText.includes(keyword.toLowerCase())) {
                    const service = this.serviceTypeMapping[keyword];
                    if (service) {
                        return {
                            success: true,
                            subCategoryId: service.id,
                            subCategoryName: service.name,
                            method: 'keyword_priority',
                            reason: `基于高优先级关键词 "${keyword}" 匹配`,
                            confidence: 0.9
                        };
                    }
                }
            }

            // 方法3: 通用关键词匹配
            for (const [keyword, service] of Object.entries(this.serviceTypeMapping)) {
                if (searchText.includes(keyword.toLowerCase())) {
                    return {
                        success: true,
                        subCategoryId: service.id,
                        subCategoryName: service.name,
                        method: 'keyword_exact',
                        reason: `基于关键词 "${keyword}" 精确匹配`,
                        confidence: 0.85
                    };
                }
            }

            // 方法4: 基于位置信息的智能判断
            const airportKeywords = [
                'airport', 'klia', 'klia2', 'lcct', 'subang', 'sultan abdul aziz shah',
                '机场', '航站楼', '候机楼', 'terminal', 'departure', 'arrival'
            ];
            
            const isPickupFromAirport = airportKeywords.some(keyword => 
                pickupLocation.toLowerCase().includes(keyword)
            );
            const isDropoffToAirport = airportKeywords.some(keyword => 
                dropoffLocation.toLowerCase().includes(keyword)
            );
            
            // 机场服务判断
            if (isPickupFromAirport && !isDropoffToAirport) {
                const pickupService = this.serviceTypeMapping['pickup'] || { id: 7, name: 'Pickup' };
                return {
                    success: true,
                    subCategoryId: pickupService.id,
                    subCategoryName: pickupService.name,
                    method: 'location_analysis',
                    reason: '基于机场接机位置判断',
                    confidence: 0.8
                };
            }

            if (!isPickupFromAirport && isDropoffToAirport) {
                const dropoffService = this.serviceTypeMapping['dropoff'] || { id: 8, name: 'Dropoff' };
                return {
                    success: true,
                    subCategoryId: dropoffService.id,
                    subCategoryName: dropoffService.name,
                    method: 'location_analysis',
                    reason: '基于机场送机位置判断',
                    confidence: 0.8
                };
            }

            if (isPickupFromAirport && isDropoffToAirport) {
                const transferService = this.serviceTypeMapping['pickup'] || { id: 7, name: 'Pickup' };
                return {
                    success: true,
                    subCategoryId: transferService.id,
                    subCategoryName: transferService.name,
                    method: 'location_analysis',
                    reason: '基于机场间转乘判断',
                    confidence: 0.75
                };
            }

            // 方法5: 基于特殊目的地判断（包车服务）
            const destinations = {
                genting: ['genting', 'resorts world genting', '云顶', '云顶高原', 'awana'],
                melaka: ['melaka', 'malacca', '马六甲', 'historical city'],
                sky_mirror: ['sky mirror', 'kuala selangor', '天空之镜', 'sasaran'],
                fireflies: ['fireflies', 'kampung kuantan', '萤火虫', 'kuala selangor']
            };

            for (const [dest, keywords] of Object.entries(destinations)) {
                const hasDestination = keywords.some(keyword => 
                    searchText.includes(keyword.toLowerCase())
                );
                
                if (hasDestination) {
                    const service = this.serviceTypeMapping[dest];
                    if (service) {
                        return {
                            success: true,
                            subCategoryId: service.id,
                            subCategoryName: service.name,
                            method: 'destination_analysis',
                            reason: `基于目的地 "${dest}" 判断`,
                            confidence: 0.85
                        };
                    }
                }
            }

            // 方法6: 基于航班信息判断
            if (flightNumber && flightNumber.trim()) {
                const pickupService = this.serviceTypeMapping['pickup'] || { id: 7, name: 'Pickup' };
                return {
                    success: true,
                    subCategoryId: pickupService.id,
                    subCategoryName: pickupService.name,
                    method: 'flight_analysis',
                    reason: '基于航班信息判断为接机服务',
                    confidence: 0.7
                };
            }

            // 方法7: 使用默认服务类型
            const defaultService = this.serviceTypeMapping['pickup'] || { id: 7, name: 'Pickup' };
            return {
                success: true,
                subCategoryId: defaultService.id,
                subCategoryName: defaultService.name,
                method: 'default_fallback',
                reason: '使用默认接机服务',
                confidence: 0.6
            };

        } catch (error) {
            return {
                success: false,
                error: error.message,
                method: 'error_fallback'
            };
        }
    }

    /**
     * 选择后台用户
     * @param {Object} orderData - 订单数据
     * @returns {Object} 用户选择结果
     */
    selectBackendUser(orderData) {
        try {
            const ota = orderData.ota || '';
            const serviceType = orderData.service_type || '';
            const remarks = orderData.remarks || '';
            const passengerCount = parseInt(orderData.passenger_count) || 1;
            const pickupLocation = orderData.pickup_location || '';
            const destination = orderData.destination || '';
            const flightNumber = orderData.flight_number || '';
            const pickupTime = orderData.pickup_time || '';

            // 构建搜索文本
            const searchText = `${ota} ${serviceType} ${remarks} ${pickupLocation} ${destination}`.toLowerCase();

            // 方法1: 基于OTA平台的用户分配（高优先级）
            const otaMapping = {
                'booking.com': 'operator',
                'agoda': 'operator', 
                'expedia': 'operator',
                'trip.com': 'operator',
                'ctrip': 'operator',
                '携程': 'operator',
                'gomyhire': 'gomyhire',
                'chong_dealer': 'chong_dealer',
                'klook': 'operator',
                'viator': 'operator',
                'getyourguide': 'operator'
            };

            for (const [platform, userType] of Object.entries(otaMapping)) {
                if (searchText.includes(platform.toLowerCase())) {
                    const user = this.backendUserMapping[userType] || this.backendUserMapping['default'];
                    return {
                        success: true,
                        userId: user.id,
                        userName: user.name,
                        method: 'ota_mapping',
                        reason: `基于OTA平台 "${platform}" 分配`,
                        confidence: 0.9
                    };
                }
            }

            // 方法2: 基于服务类型的用户分配
            const serviceUserMapping = {
                'airport': 'operator',
                'charter': 'operator',
                'luxury': 'admin',
                'vip': 'admin',
                'premium': 'admin',
                'business': 'admin',
                'tour': 'operator',
                'sightseeing': 'operator',
                'transfer': 'operator'
            };

            for (const [service, userType] of Object.entries(serviceUserMapping)) {
                if (searchText.includes(service)) {
                    const user = this.backendUserMapping[userType] || this.backendUserMapping['default'];
                    return {
                        success: true,
                        userId: user.id,
                        userName: user.name,
                        method: 'service_mapping',
                        reason: `基于服务类型 "${service}" 分配`,
                        confidence: 0.8
                    };
                }
            }

            // 方法3: 基于乘客数量的用户分配
            if (passengerCount >= 8) {
                const user = this.backendUserMapping['admin'] || this.backendUserMapping['default'];
                return {
                    success: true,
                    userId: user.id,
                    userName: user.name,
                    method: 'passenger_count',
                    reason: `基于大团体乘客数量 (${passengerCount}人) 分配管理员`,
                    confidence: 0.8
                };
            }

            // 方法4: 基于目的地复杂度的用户分配
            const complexDestinations = [
                'genting', '云顶', 'malacca', '马六甲', 'kuala selangor', '雪兰莪',
                'sekinchan', '适耕庄', 'firefly', '萤火虫', 'sky mirror', '天空之镜',
                'cameron highlands', '金马仑', 'fraser hill', '福隆港',
                'port dickson', '波德申', 'bentong', '文冬'
            ];

            for (const dest of complexDestinations) {
                if (searchText.includes(dest.toLowerCase())) {
                    const user = this.backendUserMapping['operator'] || this.backendUserMapping['default'];
                    return {
                        success: true,
                        userId: user.id,
                        userName: user.name,
                        method: 'destination_complexity',
                        reason: `基于复杂目的地 "${dest}" 分配专业操作员`,
                        confidence: 0.8
                    };
                }
            }

            // 方法5: 基于时间敏感性的用户分配（航班服务）
            if (flightNumber && flightNumber.trim()) {
                const user = this.backendUserMapping['operator'] || this.backendUserMapping['default'];
                return {
                    success: true,
                    userId: user.id,
                    userName: user.name,
                    method: 'flight_service',
                    reason: '基于航班服务的时间敏感性分配操作员',
                    confidence: 0.8
                };
            }

            // 方法6: 基于关键词匹配的用户分配
            const keywordMapping = {
                'urgent': 'admin',
                '紧急': 'admin',
                'vip': 'admin',
                'important': 'admin',
                '重要': 'admin',
                'special': 'admin',
                '特殊': 'admin',
                'wedding': 'admin',
                '婚礼': 'admin',
                'business': 'admin',
                '商务': 'admin',
                'corporate': 'admin',
                '企业': 'admin'
            };

            for (const [keyword, userType] of Object.entries(keywordMapping)) {
                if (searchText.includes(keyword.toLowerCase())) {
                    const user = this.backendUserMapping[userType] || this.backendUserMapping['default'];
                    return {
                        success: true,
                        userId: user.id,
                        userName: user.name,
                        method: 'keyword_priority',
                        reason: `基于优先级关键词 "${keyword}" 分配`,
                        confidence: 0.7
                    };
                }
            }

            // 方法7: 基于工作负载均衡的用户分配
            // 这里可以根据实际的工作负载数据进行分配
            // 目前使用简单的轮询策略
            const availableUsers = ['operator', 'admin', 'customer_service'];
            const currentHour = new Date().getHours();
            let selectedUserType;

            if (currentHour >= 9 && currentHour <= 18) {
                // 工作时间优先分配操作员
                selectedUserType = 'operator';
            } else {
                // 非工作时间分配客服
                selectedUserType = 'customer_service';
            }

            const user = this.backendUserMapping[selectedUserType] || this.backendUserMapping['default'];
            return {
                success: true,
                userId: user.id,
                userName: user.name,
                method: 'workload_balance',
                reason: `基于工作时间 (${currentHour}:00) 的负载均衡分配`,
                confidence: 0.6
            };

        } catch (error) {
            // 错误回退：使用默认用户
            const fallbackUser = this.backendUserMapping['default'] || this.backendUserMapping['fallback'] || {
                id: 1,
                name: 'Default User'
            };
            
            return {
                success: false,
                userId: fallbackUser.id,
                userName: fallbackUser.name,
                error: error.message,
                method: 'error_fallback',
                reason: '发生错误，使用默认用户',
                confidence: 0.3
            };
        }
    }

    /**
     * 获取智能选择统计信息
     * @returns {Object} 统计信息
     */
    getSelectionStats() {
        return {
            initialized: this.initialized,
            vehicleTypes: Object.keys(this.vehicleTypeMapping.passengerCount).length,
            vehicleKeywords: Object.keys(this.vehicleTypeMapping.keywords).length,
            serviceTypes: Object.keys(this.serviceTypeMapping).length,
            backendUsers: Object.keys(this.backendUserMapping).length,
            lastUpdate: this.lastUpdate || null
        };
    }

    /**
     * 重置映射表到默认状态
     */
    resetToDefaults() {
        this.logger.info('SmartSelection', '重置映射表到默认状态');
        // 重新初始化默认映射表
        this.constructor();
        this.initialized = false;
    }

    /**
     * @function enhanceServiceTypeSelection - 增强服务类型选择算法
     * @param {Object} orderData - 订单数据
     * @returns {Object} 增强的服务类型选择结果
     */
    enhanceServiceTypeSelection(orderData) {
        try {
            const startTime = performance.now();
            
            // 构建搜索上下文
            const searchContext = {
                text: this.buildServiceSearchText(orderData),
                orderData: orderData,
                timestamp: Date.now()
            };
            
            const candidates = [];
            
            // 方法1: 传统方法增强版
            const traditionalResult = this.selectServiceType(orderData);
            if (traditionalResult.success) {
                const enhancedScore = this.accuracyCalculator.calculateCompositeScore(
                    traditionalResult, searchContext);
                candidates.push({
                    ...traditionalResult,
                    enhancedScore: enhancedScore,
                    finalConfidence: enhancedScore.confidence
                });
            }
            
            // 方法2: 关键词增强匹配
            const keywordResult = this.performServiceKeywordMatching(orderData, searchContext);
            if (keywordResult.success) {
                const enhancedScore = this.accuracyCalculator.calculateCompositeScore(
                    keywordResult, searchContext);
                candidates.push({
                    ...keywordResult,
                    enhancedScore: enhancedScore,
                    finalConfidence: enhancedScore.confidence
                });
            }
            
            // 方法3: 上下文分析
            const contextResult = this.performServiceContextAnalysis(orderData, searchContext);
            if (contextResult.success) {
                const enhancedScore = this.accuracyCalculator.calculateCompositeScore(
                    contextResult, searchContext);
                candidates.push({
                    ...contextResult,
                    enhancedScore: enhancedScore,
                    finalConfidence: enhancedScore.confidence
                });
            }
            
            // 候选方案排序和选择
            if (candidates.length > 0) {
                candidates.sort((a, b) => b.finalConfidence - a.finalConfidence);
                
                const bestCandidate = candidates[0];
                const processingTime = performance.now() - startTime;
                
                return {
                    ...bestCandidate,
                    algorithmVersion: this.version,
                    processingTime: processingTime,
                    candidateCount: candidates.length,
                    enhancedFeatures: this.algorithmEnhancements
                };
            }
            
            // 回退到传统方法
            return traditionalResult;
            
        } catch (error) {
            return this.selectServiceType(orderData);
        }
    }
    
    /**
     * @function buildServiceSearchText - 构建服务类型搜索文本
     * @param {Object} orderData - 订单数据
     * @returns {string} 搜索文本
     */
    buildServiceSearchText(orderData) {
        const fields = [
            orderData.service_type || '',
            orderData.order_type || '',
            orderData.pickup_location || '',
            orderData.dropoff_location || '',
            orderData.destination || '',
            orderData.remarks || '',
            orderData.ota || ''
        ];
        
        return fields.join(' ').toLowerCase().trim();
    }
    
    /**
     * @function performServiceKeywordMatching - 执行服务类型关键词匹配
     * @param {Object} orderData - 订单数据
     * @param {Object} searchContext - 搜索上下文
     * @returns {Object} 关键词匹配结果
     */
    performServiceKeywordMatching(orderData, searchContext) {
        let bestMatch = null;
        let maxScore = 0;
        
        for (const [keyword, service] of Object.entries(this.serviceTypeMapping)) {
            const matchResult = this.enhancedMatchingEngine.enhancedKeywordMatch(
                searchContext.text, keyword);
            
            if (matchResult.matched && matchResult.score > maxScore) {
                bestMatch = {
                    success: true,
                    subCategoryId: service.id,
                    subCategoryName: service.name,
                    method: 'enhanced_service_keyword_match',
                    reason: `增强服务关键词匹配: "${matchResult.matchedTerm}" (${matchResult.method})`,
                    confidence: matchResult.score,
                    category: service.category,
                    matchDetails: matchResult
                };
                maxScore = matchResult.score;
            }
        }
        
        return bestMatch || { success: false };
    }
    
    /**
     * @function performServiceContextAnalysis - 执行服务类型上下文分析
     * @param {Object} orderData - 订单数据
     * @param {Object} searchContext - 搜索上下文
     * @returns {Object} 上下文分析结果
     */
    performServiceContextAnalysis(orderData, searchContext) {
        const flightNumber = orderData.flight_number || '';
        const pickupLocation = (orderData.pickup_location || '').toLowerCase();
        const dropoffLocation = (orderData.dropoff_location || '').toLowerCase();
        const serviceTime = orderData.service_time || '';
        
        // 机场服务上下文分析
        const airportKeywords = ['airport', 'klia', 'klia2', 'lcct', 'subang', '机场', '航站楼'];
        const isPickupFromAirport = airportKeywords.some(keyword => 
            pickupLocation.includes(keyword));
        const isDropoffToAirport = airportKeywords.some(keyword => 
            dropoffLocation.includes(keyword));
        
        // 基于位置和航班信息的智能判断
        if (flightNumber && isPickupFromAirport) {
            return {
                success: true,
                subCategoryId: 7,
                subCategoryName: 'Pickup',
                method: 'contextual_flight_analysis',
                reason: `上下文分析: 有航班号(${flightNumber})且从机场出发 → 接机服务`,
                confidence: 0.9,
                category: 'Airport'
            };
        }
        
        if (isDropoffToAirport && !isPickupFromAirport) {
            return {
                success: true,
                subCategoryId: 8,
                subCategoryName: 'Dropoff',
                method: 'contextual_location_analysis',
                reason: `上下文分析: 目的地是机场 → 送机服务`,
                confidence: 0.85,
                category: 'Airport'
            };
        }
        
        // 旅游景点上下文分析
        const touristDestinations = ['genting', '云顶', 'malacca', '马六甲', 'sekinchan', '适耕庄'];
        for (const dest of touristDestinations) {
            if (searchContext.text.includes(dest.toLowerCase())) {
                return {
                    success: true,
                    subCategoryId: dest.includes('genting') || dest.includes('云顶') ? 9 : 10,
                    subCategoryName: dest.includes('genting') || dest.includes('云顶') ? 'KL to genting' : 'KL to melaka',
                    method: 'contextual_tourism_analysis',
                    reason: `上下文分析: 检测到旅游目的地"${dest}" → 包车服务`,
                    confidence: 0.8,
                    category: 'Chartered'
                };
            }
        }
        
        return { success: false };
    }
    
    /**
     * @function getEnhancedPerformanceMetrics - 获取增强性能指标
     * @returns {Object} 详细的性能指标和统计信息
     */
    getEnhancedPerformanceMetrics() {
        const learningMetrics = this.learningEngine.getPerformanceMetrics();
        const basicStats = this.getSelectionStats();
        
        // 计算方法成功率统计
        const methodAccuracy = {};
        const methods = ['passenger_count_exact', 'enhanced_keyword_match', 'smart_range_match', 
                        'contextual_analysis', 'service_mapping', 'ota_mapping'];
        
        methods.forEach(method => {
            methodAccuracy[method] = {
                accuracy: this.learningEngine.calculateMethodAccuracy(method),
                weight: this.learningEngine.getOptimalWeight(method)
            };
        });
        
        return {
            ...basicStats,
            ...learningMetrics,
            algorithmVersion: this.version,
            enhancedFeatures: this.algorithmEnhancements,
            methodAccuracy: methodAccuracy,
            performanceProfile: {
                averageProcessingTime: this.calculateAverageProcessingTime(),
                memoryUsage: this.calculateMemoryUsage(),
                cacheHitRate: this.calculateCacheHitRate()
            },
            recommendations: this.generatePerformanceRecommendations()
        };
    }
    
    /**
     * @function calculateAverageProcessingTime - 计算平均处理时间
     * @returns {number} 平均处理时间(毫秒)
     */
    calculateAverageProcessingTime() {
        const recentSelections = this.learningEngine.learningData.successfulMatches.slice(-100);
        if (recentSelections.length === 0) return 0;
        
        const totalTime = recentSelections.reduce((sum, record) => 
            sum + (record.processingTime || 0), 0);
        return totalTime / recentSelections.length;
    }
    
    /**
     * @function calculateMemoryUsage - 计算内存使用情况
     * @returns {Object} 内存使用统计
     */
    calculateMemoryUsage() {
        const learningDataSize = JSON.stringify(this.learningEngine.learningData).length;
        const mappingDataSize = JSON.stringify({
            vehicleTypes: this.vehicleTypeMapping,
            serviceTypes: this.serviceTypeMapping,
            backendUsers: this.backendUserMapping
        }).length;
        
        return {
            learningDataSize: learningDataSize,
            mappingDataSize: mappingDataSize,
            totalSize: learningDataSize + mappingDataSize,
            estimatedMemoryMB: (learningDataSize + mappingDataSize) / (1024 * 1024)
        };
    }
    
    /**
     * @function calculateCacheHitRate - 计算缓存命中率
     * @returns {number} 缓存命中率
     */
    calculateCacheHitRate() {
        const totalSelections = this.learningEngine.performanceMetrics.totalSelections;
        const exactMatches = this.learningEngine.learningData.successfulMatches.filter(
            record => record.method === 'passenger_count_exact').length;
        
        return totalSelections > 0 ? exactMatches / totalSelections : 0;
    }
    
    /**
     * @function generatePerformanceRecommendations - 生成性能优化建议
     * @returns {Array} 优化建议列表
     */
    generatePerformanceRecommendations() {
        const recommendations = [];
        const metrics = this.learningEngine.getPerformanceMetrics();
        
        // 准确率建议
        if (metrics.accuracyRate < 0.8) {
            recommendations.push({
                type: 'accuracy',
                priority: 'high',
                message: '当前匹配准确率较低，建议检查数据质量或调整算法参数',
                suggestion: '增加更多的训练样本或调整匹配权重'
            });
        }
        
        // 性能建议
        const avgProcessingTime = this.calculateAverageProcessingTime();
        if (avgProcessingTime > 100) {
            recommendations.push({
                type: 'performance',
                priority: 'medium',
                message: '算法处理时间较长，可能影响用户体验',
                suggestion: '优化关键词匹配算法或减少候选方案数量'
            });
        }
        
        // 内存建议
        const memoryUsage = this.calculateMemoryUsage();
        if (memoryUsage.estimatedMemoryMB > 5) {
            recommendations.push({
                type: 'memory',
                priority: 'medium',
                message: '学习数据占用内存较大',
                suggestion: '定期清理旧的学习数据或实施数据压缩'
            });
        }
        
        // 学习建议
        if (metrics.dataSize.successfulMatches < 50) {
            recommendations.push({
                type: 'learning',
                priority: 'low',
                message: '学习样本数量不足，可能影响自适应能力',
                suggestion: '鼓励用户提供更多反馈以改善学习效果'
            });
        }
        
        return recommendations;
    }

    /**
     * @function handleApiSyncStatusChange - 处理API同步状态变更
     * @param {string} status - 同步状态
     * @param {Object} data - 状态数据
     */
    handleApiSyncStatusChange(status, data) {
        switch (status) {
            case 'sync_started':
                this.logger.info('SmartSelection', 'API数据同步开始');
                break;
                
            case 'sync_success':
                this.logger.info('SmartSelection', 'API数据同步成功', {
                    backendUsers: data.backendUsers,
                    subCategories: data.subCategories,
                    carTypes: data.carTypes
                });
                
                // 数据同步成功后重新初始化映射表
                this.updateMappingFromAppState();
                break;
                
            case 'sync_error':
                this.logger.error('SmartSelection', 'API数据同步失败', {
                    error: data.error,
                    retryCount: data.retryCount
                });
                break;
                
            default:
                this.logger.debug('SmartSelection', `API同步状态变更: ${status}`, data);
        }
    }
    
    /**
     * @function forceApiSync - 强制API数据同步
     * @returns {Promise<Object>} 同步结果
     */
    async forceApiSync() {
        this.logger.info('SmartSelection', '强制执行API数据同步');
        return await this.apiSyncManager.forceSync();
    }
    
    /**
     * @function getApiSyncStatus - 获取API同步状态
     * @returns {Object} 同步状态信息
     */
    getApiSyncStatus() {
        return this.apiSyncManager.getSyncStatus();
    }
}