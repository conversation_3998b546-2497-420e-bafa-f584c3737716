/**
 * @file config.js - 系统统一配置文件
 * @description 整合所有配置信息，提供统一的配置管理
 * <AUTHOR> IDE
 * @created_at 2024-12-19
 * @updated_at 2024-12-19
 */

// 系统配置
const SYSTEM_CONFIG = {
    // 系统信息
    SYSTEM_INFO: {
        name: 'OTA订单处理系统',
        version: '2.0.0',
        description: '基于Gemini的轻量化订单解析系统',
        lastUpdated: '2024-12-19'
    },

    // API配置
    API: {
        // GoMyHire API基础URL
        BASE_URL: 'https://staging.gomyhire.com.my/api',
        
        // Gemini AI API配置（主要LLM）
        GEMINI: {
            // 请替换为您的实际Gemini API Key
            API_KEY: 'AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s',
            API_URL: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent',
            MODEL_CONFIG: {
                temperature: 0.1,
                topK: 40,
                topP: 0.95,
                maxOutputTokens: 2048
            },
            TIMEOUT: 30000,
            MAX_RETRIES: 1,
            PRIORITY: 2
        },

        // Google Vision API配置（图片分析专用）
        GOOGLE_VISION: {
            API_KEY: 'AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s',
            API_URL: 'https://vision.googleapis.com/v1/images:annotate',
            FEATURES: {
                TEXT_DETECTION: 'TEXT_DETECTION',
                DOCUMENT_TEXT_DETECTION: 'DOCUMENT_TEXT_DETECTION',
                LABEL_DETECTION: 'LABEL_DETECTION',
                OBJECT_LOCALIZATION: 'OBJECT_LOCALIZATION'
            },
            MAX_RESULTS: 10,
            TIMEOUT: 15000
        },

        // Google Maps Places API配置（地址搜索专用）
        GOOGLE_MAPS: {
            API_KEY: 'AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s',
            PLACES_API_URL: 'https://maps.googleapis.com/maps/api/place',
            GEOCODING_API_URL: 'https://maps.googleapis.com/maps/api/geocode/json',
            AUTOCOMPLETE_URL: 'https://maps.googleapis.com/maps/api/place/autocomplete/json',
            PLACE_DETAILS_URL: 'https://maps.googleapis.com/maps/api/place/details/json',
            // 搜索配置
            SEARCH_CONFIG: {
                components: 'country:my', // 限制在马来西亚
                types: 'establishment|geocode', // 地点类型
                language: 'zh-CN,en', // 支持中文和英文
                radius: 50000, // 搜索半径50公里
                strictbounds: false // 不严格限制边界
            },
            // 地点字段配置
            PLACE_FIELDS: [
                'place_id',
                'formatted_address', 
                'name',
                'geometry',
                'types',
                'address_components'
            ],
            TIMEOUT: 10000,
            MAX_RESULTS: 5,
            DEBOUNCE_DELAY: 300 // 输入防抖延迟（毫秒）
        }
    },
    
    // 本地存储键名
    STORAGE_KEYS: {
        TOKEN: 'ota_system_token',
        USER_INFO: 'ota_system_user',
        BACKEND_USERS: 'ota_backend_users',
        SUB_CATEGORIES: 'ota_sub_categories',
        CAR_TYPES: 'ota_car_types',
        LAST_LOGIN: 'ota_last_login'
    },
    
    // 默认登录信息
    DEFAULT_LOGIN: {
        email: '<EMAIL>',
        password: '1234'
    },
    
    // OTA类型配置
    OTA_TYPES: {
        'chong-dealer': {
            name: 'Chong Dealer',
            description: '重庆经销商订单处理',
            enabled: true,
            priority: 1,
            keywordPatterns: [
                'CHONG 车头',
                '收单&进单',
                '\\*京鱼\\*',
                '\\*野马\\*',
                '\\*小野马\\*',
                '\\*kenny\\*',
                '\\*迹象\\*',
                '\\*鲸鱼\\*',
                '用车地点[:：]',
                '用车时间[:：]',
                '客人姓名[:：]',
                '接送机类型[:：]',
                '结算[:：]',
                '价格[:：].*?Rm',
                '举牌',
                '机场转乘',
                '包车.*?小时',
                // 添加通用订单关键词以提高识别率
                '接机[:：]?\\s*[A-Z]{1,3}\\d{2,4}',  // 接机: D7333
                '\\d{1,2}[:：]\\d{2}抵达',           // 11:40抵达
                '联系人[:：]',                        // 联系人：
                '人数[:：]\\s*\\d+',                 // 人数：2
                '车型[:：]',                          // 车型：
                '酒店[:：].*?[Hh]otel',              // 酒店：...Hotel
                '\\d+月\\d+日.*?接机'                // 5月30日...接机
            ],
            minimumMatches: 2,
            confidence: 0.8
        },
        'auto': {
            name: '自动识别',
            description: '根据订单内容自动识别OTA类型',
            enabled: true
        },
        'fallback': {
            name: '通用回退模板',
            description: '通用订单识别模板，作为fallback机制处理无法识别特定OTA类型的订单',
            enabled: true,
            priority: 99,
            keywordPatterns: [
                '[A-Z]{1,3}\\d{2,4}',           // 航班号模式: CZ8301, AK5749
                '\\d{1,2}[:：]\\d{2}',          // 时间模式: 22:20, 15：30
                '\\d{1,2}[月\\.]\\d{1,2}日?',   // 日期模式: 1.28, 2月15日
                '姓名[:：]|客人[:：]|预定人[:：]', // 姓名标识
                '接机|送机|包车|点对点|机场转乘',   // 服务类型
                '\\d+人|\\d+大人|\\d+位',        // 人数模式: 2人, 3大人
                '酒店[:：]|住宿[:：]|入住[:：]|地址[:：]', // 地点标识
                '航班[:：]|航班号[:：]|flight',   // 航班标识
                '联系[:：]|电话[:：]|微信[:：]',   // 联系方式
                '\\d{10,15}'                    // 电话模式
            ],
            minimumMatches: 2,
            confidence: 0.6
        },
        'other': {
            name: '其他',
            description: '通用订单处理',
            enabled: true
        }
    },

    // 智能选择规则
    SMART_SELECTION: {
        enabled: true,
        rules: {
            carTypeByPassengerCount: {
                '1': 1, '2': 1, '3': 1, '4': 1, '5': 2,
                '6': 2, '7': 2, '8': 3, '9': 3, '10': 3
            },
            subCategoryByServiceType: {
                '接机': 1, '送机': 2, '包车': 3, '点对点': 4, '机场转乘': 5
            },
            // 后台用户分配：默认第一个用户
            backendUserByOta: {
                'default': 1, 'chong_dealer': 1, 'gomyhire': 1, 'fallback': 1
            },
            // 服务类型映射：LLM返回类型到系统类型的映射
            serviceTypeMapping: {
                '接机': 1, 'pickup': 1, 'airport_pickup': 1,
                '送机': 2, 'dropoff': 2, 'airport_dropoff': 2,
                '包车': 3, 'charter': 3, 'hourly_charter': 3,
                '点对点': 4, 'point_to_point': 4, 'p2p': 4,
                '机场转乘': 5, 'airport_transfer': 5, 'transfer': 5
            }
        }
    },

    // 文件上传配置
    UPLOAD: {
        MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
        ALLOWED_TYPES: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
        MAX_FILES: 10
    },
    
    // 系统设置
    SYSTEM: {
        AUTO_SAVE_INTERVAL: 30000, // 30秒自动保存
        SESSION_TIMEOUT: 24 * 60 * 60 * 1000, // 24小时
        RETRY_ATTEMPTS: 3,
        RETRY_DELAY: 1000,
        MAX_LOG_ENTRIES: 1000
    },

    // 性能配置
    PERFORMANCE: {
        targets: {
            otaDetectionTime: 100,
            llmProcessingTime: 15000,
            formatConversionTime: 50,
            totalProcessingTime: 17000
        },
        monitoring: {
            enabled: true,
            logSlowRequests: true,
            slowRequestThreshold: 20000
        }
    },

    // 错误处理配置
    ERROR_HANDLING: {
        retryPolicy: {
            maxRetries: 3,
            backoffMultiplier: 2,
            initialDelay: 1000
        },
        fallbackChain: ['gemini', 'fallback_parser']
    },

    // LLM优化配置
    LLM_OPTIMIZATION: {
        // 缓存配置
        cache: {
            enabled: true,
            maxSize: 100,
            ttl: 5 * 60 * 1000, // 5分钟TTL
            keyStrategy: 'content_hash' // content_hash 或 simple
        },
        // 批处理配置
        batchProcessing: {
            enabled: true,
            defaultBatchSize: 5,
            maxConcurrency: 10,
            batchDelay: 100, // 批次间延迟(ms)
            timeoutPerBatch: 60000 // 每批次超时时间
        },
        // 动态模型加载配置
        dynamicLoading: {
            enabled: true,
            loadStrategy: 'dynamic', // 'eager', 'lazy', 'dynamic'
            chunkSize: 512, // 分块大小
            preloadModules: ['core', 'parser'], // 预加载模块
            lazyLoadThreshold: 1000, // 懒加载阈值(ms)
            memoryManagement: {
                enabled: true,
                maxMemoryUsage: 100 * 1024 * 1024, // 100MB
                gcInterval: 30000 // 垃圾回收间隔(ms)
            }
        },
        // 性能监控
        performance: {
            enabled: true,
            trackMetrics: ['responseTime', 'cacheHitRate', 'errorRate'],
            alertThresholds: {
                responseTime: 10000, // 10秒
                errorRate: 0.1, // 10%
                cacheHitRate: 0.3 // 30%
            }
        }
    },

    // 验证配置
    VALIDATION: {
        orderValidation: {
            enabled: true,
            strictMode: false,
            requiredFields: ['customerName', 'serviceType'],
            recommendedFields: ['serviceDate', 'flightNumber', 'customerContact']
        },
        dateValidation: {
            enabled: true,
            allowPastDates: false,
            maxFutureDays: 365,
            defaultYear: 'current'
        }
    },

    // 日志配置
    LOGGING: {
        enabled: true,
        level: 'info',
        maxEntries: 1000,
        includeFields: {
            originalText: false,
            apiResponses: false,
            processingTime: true,
            errorDetails: true
        }
    }
};

// 导出配置（如果在模块环境中）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SYSTEM_CONFIG;
}

// 导出配置（Node.js环境）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SYSTEM_CONFIG;
}

// 全局配置（浏览器环境）
if (typeof window !== 'undefined') {
    window.SYSTEM_CONFIG = SYSTEM_CONFIG;
}
