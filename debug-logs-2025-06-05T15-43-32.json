{"exportTime": "2025-06-05T15:43:32.499Z", "totalLogs": 80, "logs": [{"timestamp": "2025-06-05T15:42:08.323Z", "level": "INFO", "module": "AddressSearch", "message": "地址搜索服务初始化", "data": {"isConfigured": true, "apiKeyPresent": true}}, {"timestamp": "2025-06-05T15:42:08.522Z", "level": "INFO", "module": "AppState", "message": "用户系统数据加载完成", "data": {"userHash": "c3VwZXJfYWRtaW5AZ29teWhpcmUuY29tX2Fub255bW91cw", "loadedCount": 3, "totalKeys": 3}}, {"timestamp": "2025-06-05T15:42:08.524Z", "level": "INFO", "module": "AppState", "message": "用户数据初始化完成", "data": {"userHash": "c3VwZXJfYWRtaW5AZ29teWhpcmUuY29tX2Fub255bW91cw", "userEmail": "<EMAIL>"}}, {"timestamp": "2025-06-05T15:42:08.527Z", "level": "INFO", "module": "应用", "message": "开始初始化OTA订单处理系统", "data": null}, {"timestamp": "2025-06-05T15:42:08.529Z", "level": "DEBUG", "module": "UI", "message": "更新连接状态", "data": null}, {"timestamp": "2025-06-05T15:42:08.530Z", "level": "DEBUG", "module": "UI", "message": "文件上传初始化完成", "data": null}, {"timestamp": "2025-06-05T15:42:08.532Z", "level": "DEBUG", "module": "应用", "message": "UI组件初始化完成", "data": null}, {"timestamp": "2025-06-05T15:42:08.534Z", "level": "DEBUG", "module": "应用", "message": "事件监听器绑定完成", "data": null}, {"timestamp": "2025-06-05T15:42:08.536Z", "level": "INFO", "module": "应用", "message": "发现已保存的认证令牌，验证有效性", "data": null}, {"timestamp": "2025-06-05T15:42:08.537Z", "level": "INFO", "module": "API", "message": "发起获取后端用户请求", "data": null}, {"timestamp": "2025-06-05T15:42:08.877Z", "level": "SUCCESS", "module": "API响应", "message": "GET https://staging.gomyhire.com.my/api/backend_users - 200 ", "data": {"type": "API_RESPONSE", "method": "GET", "url": "https://staging.gomyhire.com.my/api/backend_users", "status": 200, "timestamp": "2025-06-05T15:42:08.877Z", "responseTime": null, "responseData": {"total_count": 12, "users": [{"id": 1, "name": "Super Admin", "phone": "0162234711", "role": "Super Admin"}, {"id": 105, "name": "<PERSON>", "phone": null, "role": "Super Admin"}, {"id": 22, "name": "Zahidah1", "phone": "0132456789", "role": "Operator"}, {"id": 106, "name": "admin", "phone": null, "role": "Operator"}, {"id": 108, "name": "<PERSON><PERSON><PERSON>", "phone": "0123456789", "role": "Operator"}, {"id": 143, "name": "Kk", "phone": "012", "role": "Operator"}, {"id": 206, "name": "OperatorRinglee", "phone": "0181122334", "role": "Operator"}, {"id": 362, "name": "<PERSON><PERSON>", "phone": null, "role": "Operator"}, {"id": 110, "name": "Sub Admin", "phone": "0162234711", "role": "Sub_Admin"}, {"id": 229, "name": "test", "phone": null, "role": "Sub_Admin"}, {"id": 338, "name": "jcy1", "phone": null, "role": "Sub_Admin"}, {"id": 163, "name": "Kok1", "phone": null, "role": "Sub_Operator"}]}}}, {"timestamp": "2025-06-05T15:42:08.880Z", "level": "DEBUG", "module": "AppState", "message": "用户数据缓存已更新", "data": {"userHash": "c3VwZXJfYWRtaW5AZ29teWhpcmUuY29tX2Fub255bW91cw", "key": "backendUsers", "dataSize": 756}}, {"timestamp": "2025-06-05T15:42:08.881Z", "level": "SUCCESS", "module": "API", "message": "获取后端用户成功", "data": {"userCount": 12, "userNames": ["Super Admin", "<PERSON>", "Zahidah1", "admin", "<PERSON><PERSON><PERSON>"]}}, {"timestamp": "2025-06-05T15:42:08.882Z", "level": "SUCCESS", "module": "应用", "message": "认证令牌有效", "data": null}, {"timestamp": "2025-06-05T15:42:08.884Z", "level": "DEBUG", "module": "UI", "message": "隐藏登录模态框", "data": null}, {"timestamp": "2025-06-05T15:42:08.885Z", "level": "DEBUG", "module": "UI", "message": "显示主界面", "data": null}, {"timestamp": "2025-06-05T15:42:08.903Z", "level": "INFO", "module": "应用", "message": "检测LLM服务连接状态", "data": null}, {"timestamp": "2025-06-05T15:42:08.904Z", "level": "INFO", "module": "Gemini", "message": "开始检测Gemini API连接状态", "data": {"previousStatus": "checking", "consecutiveFailures": 0}}, {"timestamp": "2025-06-05T15:42:08.907Z", "level": "INFO", "module": "应用", "message": "加载系统数据", "data": null}, {"timestamp": "2025-06-05T15:42:08.908Z", "level": "INFO", "module": "API", "message": "发起获取后端用户请求", "data": null}, {"timestamp": "2025-06-05T15:42:08.912Z", "level": "INFO", "module": "API", "message": "发起获取子分类请求", "data": null}, {"timestamp": "2025-06-05T15:42:08.915Z", "level": "INFO", "module": "API", "message": "发起获取车型请求", "data": null}, {"timestamp": "2025-06-05T15:42:09.022Z", "level": "SUCCESS", "module": "API响应", "message": "GET https://staging.gomyhire.com.my/api/backend_users - 200 ", "data": {"type": "API_RESPONSE", "method": "GET", "url": "https://staging.gomyhire.com.my/api/backend_users", "status": 200, "timestamp": "2025-06-05T15:42:09.022Z", "responseTime": null, "responseData": {"total_count": 12, "users": [{"id": 1, "name": "Super Admin", "phone": "0162234711", "role": "Super Admin"}, {"id": 105, "name": "<PERSON>", "phone": null, "role": "Super Admin"}, {"id": 22, "name": "Zahidah1", "phone": "0132456789", "role": "Operator"}, {"id": 106, "name": "admin", "phone": null, "role": "Operator"}, {"id": 108, "name": "<PERSON><PERSON><PERSON>", "phone": "0123456789", "role": "Operator"}, {"id": 143, "name": "Kk", "phone": "012", "role": "Operator"}, {"id": 206, "name": "OperatorRinglee", "phone": "0181122334", "role": "Operator"}, {"id": 362, "name": "<PERSON><PERSON>", "phone": null, "role": "Operator"}, {"id": 110, "name": "Sub Admin", "phone": "0162234711", "role": "Sub_Admin"}, {"id": 229, "name": "test", "phone": null, "role": "Sub_Admin"}, {"id": 338, "name": "jcy1", "phone": null, "role": "Sub_Admin"}, {"id": 163, "name": "Kok1", "phone": null, "role": "Sub_Operator"}]}}}, {"timestamp": "2025-06-05T15:42:09.023Z", "level": "DEBUG", "module": "AppState", "message": "用户数据缓存已更新", "data": {"userHash": "c3VwZXJfYWRtaW5AZ29teWhpcmUuY29tX2Fub255bW91cw", "key": "backendUsers", "dataSize": 756}}, {"timestamp": "2025-06-05T15:42:09.025Z", "level": "SUCCESS", "module": "API", "message": "获取后端用户成功", "data": {"userCount": 12, "userNames": ["Super Admin", "<PERSON>", "Zahidah1", "admin", "<PERSON><PERSON><PERSON>"]}}, {"timestamp": "2025-06-05T15:42:09.035Z", "level": "SUCCESS", "module": "API响应", "message": "GET https://staging.gomyhire.com.my/api/sub_category - 200 ", "data": {"type": "API_RESPONSE", "method": "GET", "url": "https://staging.gomyhire.com.my/api/sub_category", "status": 200, "timestamp": "2025-06-05T15:42:09.035Z", "responseTime": null, "responseData": {"total_count": 11, "categories": [{"id": 7, "main_category": "Airport", "name": "Pickup", "preset_data": {"order_type": "pickup", "ota": null, "driving_region": "KL - RM - Kuala Lumpur", "languages": ["JP - Japanese", "MY - Malay", "CN - Chinese", "EN - English", "KR - Korean", "TML - Tamil"], "extra_requirement": "Pickup at the right time please!"}, "required_fields": ["Customer Name", "Customer Contact", "Driving Region", "Pickup Address", "Pickup Date", "Pickup Time", "Destination Address", "Passenger Number", "Driver Fee"]}, {"id": 8, "main_category": "Airport", "name": "Dropoff", "preset_data": {"order_type": "dropoff", "ota": null, "driving_region": "KL - RM - Kuala Lumpur", "languages": ["JP - Japanese", "MY - Malay", "CN - Chinese", "EN - English"], "extra_requirement": null}, "required_fields": ["Customer Name", "Customer Contact", "Driving Region", "Pickup Address", "Pickup Date", "Pickup Time", "Destination Address", "Passenger Number", "Driver Fee"]}, {"id": 9, "main_category": "Chartered", "name": "KL to genting", "preset_data": {"order_type": "charter", "ota": null, "driving_region": null, "languages": ["EN - English"], "extra_requirement": "asdafdsgghfdfgdjhfygfcvxfgdtgcbncbncghfgfhcvbncvb\r\nncbncbcbcbncb\r\nvcbcvcvn\r\ncvcnbvcbnfhjfhjf\r\nvbncvbncgcfd\r\njhfhvmnvnvbnc\r\nfhfghfhfgjhfg\r\njhbvbnvbnv\r\nnbvnbvbnv\r\nbnvbvbnvbnv\r\nnbvnbvnbvnbv"}, "required_fields": ["Customer Name", "Customer Contact", "Driving Region", "Pickup Address", "Pickup Date", "Pickup Time", "Destination Address", "Passenger Number", "Driver Fee"]}, {"id": 10, "main_category": "Chartered", "name": "KL to melaka", "preset_data": {"order_type": "charter", "ota": null, "driving_region": null, "languages": ["JP - Japanese", "MY - Malay", "CN - Chinese", "EN - English"], "extra_requirement": "9am pickup\r\nPutrajaya\r\nStad\r\nJonker street\r\nMasjid Selatan"}, "required_fields": ["Customer Name", "Customer Contact", "Driving Region", "Pickup Address", "Pickup Date", "Pickup Time", "Destination Address", "Passenger Number", "Driver Fee"]}, {"id": 11, "main_category": "Ticket", "name": "Sky mirror", "preset_data": {"order_type": "charter", "ota": "Test", "driving_region": "KL - RM - Kuala Lumpur", "languages": ["CN - Chinese", "EN - English"], "extra_requirement": null}, "required_fields": ["Customer Name", "Customer Contact", "Pickup Date", "Pickup Time", "Passenger Number"]}, {"id": 12, "main_category": "Ticket", "name": "Fireflies", "preset_data": {"order_type": "charter", "ota": null, "driving_region": "KL - RM - Kuala Lumpur", "languages": ["JP - Japanese", "MY - Malay", "CN - Chinese", "EN - English"], "extra_requirement": "Boarding on 7 pm"}, "required_fields": ["Pickup Date", "Pickup Time", "Passenger Number"]}, {"id": 15, "main_category": "Airport", "name": "携程1", "preset_data": {"order_type": "dropoff", "ota": "Test", "driving_region": "SG - SGD - Singapore", "languages": ["JP - Japanese", "MY - Malay", "CN - Chinese", "EN - English"], "extra_requirement": "携程"}, "required_fields": ["Customer Name", "Customer Contact", "Driving Region", "Pickup Address", "Pickup Date", "Pickup Time", "Destination Address", "Passenger Number", "Driver Fee"]}, {"id": 16, "main_category": "default", "name": "default", "preset_data": {"order_type": null, "ota": null, "driving_region": null, "languages": [], "extra_requirement": null}, "required_fields": ["Customer Name", "Customer Contact", "Driving Region", "Pickup Address", "Pickup Date", "Pickup Time", "Destination Address", "Passenger Number", "Driver Fee"]}, {"id": 36, "main_category": "Charter", "name": "<PERSON><PERSON><PERSON>", "preset_data": {"order_type": null, "ota": null, "driving_region": null, "languages": [], "extra_requirement": null}, "required_fields": ["Customer Name", "Customer Contact", "Driving Region", "Pickup Address", "Pickup Date", "Pickup Time", "Destination Address", "Passenger Number", "Driver Fee"]}, {"id": 41, "main_category": "<PERSON><PERSON>", "name": "Reward Sub-Category", "preset_data": {"order_type": "charter", "ota": "Test", "driving_region": "KL - RM - Kuala Lumpur", "languages": ["EN - English", "MY - Malay", "CN - Chinese"], "extra_requirement": "RM100 reward easy"}, "required_fields": ["Driver Fee"]}, {"id": 43, "main_category": "Chartered", "name": "Charter", "preset_data": {"order_type": "charter", "ota": null, "driving_region": null, "languages": [], "extra_requirement": null}, "required_fields": ["Customer Name", "Customer Contact", "Driving Region", "Pickup Address", "Pickup Date", "Pickup Time", "Destination Address", "Passenger Number", "Driver Fee"]}]}}}, {"timestamp": "2025-06-05T15:42:09.038Z", "level": "DEBUG", "module": "AppState", "message": "用户数据缓存已更新", "data": {"userHash": "c3VwZXJfYWRtaW5AZ29teWhpcmUuY29tX2Fub255bW91cw", "key": "subCategories", "dataSize": 4254}}, {"timestamp": "2025-06-05T15:42:09.039Z", "level": "SUCCESS", "module": "API", "message": "获取子分类成功", "data": {"categoryCount": 11, "categoryNames": ["Pickup", "Dropoff", "KL to genting", "KL to melaka", "Sky mirror"]}}, {"timestamp": "2025-06-05T15:42:09.042Z", "level": "SUCCESS", "module": "API响应", "message": "GET https://staging.gomyhire.com.my/api/car_types - 200 ", "data": {"type": "API_RESPONSE", "method": "GET", "url": "https://staging.gomyhire.com.my/api/car_types", "status": 200, "timestamp": "2025-06-05T15:42:09.042Z", "responseTime": null, "responseData": {"total_count": 13, "car_types": [{"id": 5, "type": "Compact 5 Seater", "seat_number": 4, "priority": 1}, {"id": 6, "type": "Comfort 5 Seater", "seat_number": 4, "priority": 2}, {"id": 15, "type": "Mid Size SUV", "seat_number": 7, "priority": 3}, {"id": 16, "type": "Standard Size MPV", "seat_number": 6, "priority": 4}, {"id": 31, "type": "Luxury Mpv", "seat_number": 6, "priority": 5}, {"id": 32, "type": "Alphard/Velfire", "seat_number": 6, "priority": 6}, {"id": 20, "type": "10 Seater MPV / Van", "seat_number": 9, "priority": 7}, {"id": 30, "type": "12 Seater MPV", "seat_number": 11, "priority": 8}, {"id": 23, "type": "14 <PERSON><PERSON> Van", "seat_number": 12, "priority": 9}, {"id": 24, "type": "18 <PERSON><PERSON> Van", "seat_number": 16, "priority": 10}, {"id": 25, "type": "30 Seat Mni Bus", "seat_number": 30, "priority": 11}, {"id": 26, "type": "44 Seater Bus", "seat_number": 44, "priority": 12}, {"id": 34, "type": "Please Refer Live Chat", "seat_number": 1, "priority": 13}]}}}, {"timestamp": "2025-06-05T15:42:09.044Z", "level": "DEBUG", "module": "AppState", "message": "用户数据缓存已更新", "data": {"userHash": "c3VwZXJfYWRtaW5AZ29teWhpcmUuY29tX2Fub255bW91cw", "key": "carTypes", "dataSize": 839}}, {"timestamp": "2025-06-05T15:42:09.046Z", "level": "SUCCESS", "module": "API", "message": "获取车型成功", "data": {"carTypeCount": 13, "carTypeNames": ["Compact 5 Seater", "Comfort 5 Seater", "Mid Size SUV", "Standard Size MPV", "Luxury Mpv"]}}, {"timestamp": "2025-06-05T15:42:09.050Z", "level": "INFO", "module": "应用", "message": "服务类型选择器保持简化版本（pickup/dropoff/charter）", "data": null}, {"timestamp": "2025-06-05T15:42:09.053Z", "level": "DEBUG", "module": "UI", "message": "UI选择器更新完成", "data": null}, {"timestamp": "2025-06-05T15:42:09.054Z", "level": "SUCCESS", "module": "应用", "message": "系统数据加载完成", "data": null}, {"timestamp": "2025-06-05T15:42:09.060Z", "level": "INFO", "module": "SmartSelection", "message": "初始化智能选择服务", "data": null}, {"timestamp": "2025-06-05T15:42:09.062Z", "level": "WARN", "module": "SmartSelection", "message": "应用状态不可用，使用默认映射", "data": null}, {"timestamp": "2025-06-05T15:42:09.063Z", "level": "SUCCESS", "module": "应用", "message": "智能选择服务初始化完成", "data": null}, {"timestamp": "2025-06-05T15:42:09.064Z", "level": "INFO", "module": "地址搜索", "message": "地址搜索服务初始化完成", "data": {"isConfigured": true, "cacheSize": 0, "activeRequests": 0, "lastConfigCheck": 1749138129064}}, {"timestamp": "2025-06-05T15:42:09.065Z", "level": "SUCCESS", "module": "应用", "message": "系统初始化完成", "data": null}, {"timestamp": "2025-06-05T15:42:09.066Z", "level": "INFO", "module": "SmartSelection", "message": "智能选择服务初始化完成", "data": null}, {"timestamp": "2025-06-05T15:42:09.069Z", "level": "INFO", "module": "SmartSelection", "message": "API数据同步成功", "data": {"backendUsers": 12, "subCategories": 4, "carTypes": 13}}, {"timestamp": "2025-06-05T15:42:09.070Z", "level": "WARN", "module": "SmartSelection", "message": "应用状态不可用，使用默认映射", "data": null}, {"timestamp": "2025-06-05T15:42:10.631Z", "level": "SUCCESS", "module": "Gemini", "message": "Gemini API连接成功", "data": {"responseTime": "1726ms", "statusChanged": false}}, {"timestamp": "2025-06-05T15:42:10.633Z", "level": "DEBUG", "module": "UI", "message": "更新LLM状态UI", "data": {"gemini": true, "geminiElements": {"light": true, "text": true, "indicator": true}}}, {"timestamp": "2025-06-05T15:42:35.575Z", "level": "INFO", "module": "应用", "message": "开始处理订单", "data": {"textLength": 116, "otaType": "auto"}}, {"timestamp": "2025-06-05T15:42:35.576Z", "level": "DEBUG", "module": "UI", "message": "显示加载状态", "data": {"message": "正在处理订单..."}}, {"timestamp": "2025-06-05T15:42:35.580Z", "level": "INFO", "module": "订单解析", "message": "开始解析订单", "data": {"textLength": 116, "specifiedOtaType": "auto"}}, {"timestamp": "2025-06-05T15:42:35.584Z", "level": "INFO", "module": "订单解析", "message": "OTA类型检测完成", "data": {"detectedType": "chong-dealer", "confidence": 0.25}}, {"timestamp": "2025-06-05T15:42:35.585Z", "level": "INFO", "module": "订单解析", "message": "使用LLM解析订单", "data": {"otaType": "chong-dealer"}}, {"timestamp": "2025-06-05T15:42:35.587Z", "level": "INFO", "module": "LLM", "message": "开始处理订单文本", "data": {"textLength": 116, "otaType": "chong-dealer"}}, {"timestamp": "2025-06-05T15:42:35.588Z", "level": "INFO", "module": "LLM", "message": "开始调用Gemini API", "data": null}, {"timestamp": "2025-06-05T15:42:35.592Z", "level": "DEBUG", "module": "Gemini", "message": "发送API请求", "data": {"textLength": 116, "otaType": "chong-dealer", "timeout": 30000}}, {"timestamp": "2025-06-05T15:42:38.524Z", "level": "DEBUG", "module": "Gemini", "message": "API请求成功", "data": {"responseTime": 2934, "contentLength": 464}}, {"timestamp": "2025-06-05T15:42:38.526Z", "level": "INFO", "module": "LLM", "message": "Gemini API调用完成", "data": {"processingTime": 2937, "success": true, "error": null}}, {"timestamp": "2025-06-05T15:42:38.527Z", "level": "SUCCESS", "module": "LLM", "message": "Gemini处理成功", "data": {"totalProcessingTime": 2940, "apiCallTime": 2937, "provider": "gemini"}}, {"timestamp": "2025-06-05T15:42:38.538Z", "level": "INFO", "module": "SmartSelection", "message": "车型选择完成", "data": {"carTypeId": 5, "carTypeName": "Compact 5 Seater", "method": "passenger_count_exact", "confidence": 0.95}}, {"timestamp": "2025-06-05T15:42:38.540Z", "level": "INFO", "module": "SmartSelection", "message": "服务类型选择完成", "data": {"subCategoryId": 7, "subCategoryName": "Pickup", "method": "simplified_mapping", "confidence": 0.9}}, {"timestamp": "2025-06-05T15:42:38.541Z", "level": "INFO", "module": "SmartSelection", "message": "后台用户选择完成", "data": {"userId": 3, "userName": "Operator", "method": "flight_service", "confidence": 0.8}}, {"timestamp": "2025-06-05T15:42:38.542Z", "level": "INFO", "module": "SmartSelection", "message": "智能选择完成", "data": {"source": "llm", "processingTime": 14, "successfulSelections": 3, "fallbacks": 0}}, {"timestamp": "2025-06-05T15:42:38.543Z", "level": "DEBUG", "module": "订单解析", "message": "智能选择已应用到LLM解析结果", "data": {"originalOrder": {}, "enhancedOrder": {"car_type_id": 5, "sub_category_id": 7, "incharge_by_backend_user_id": 3}}}, {"timestamp": "2025-06-05T15:42:38.544Z", "level": "SUCCESS", "module": "订单解析", "message": "订单解析完成", "data": {"otaType": "chong-dealer", "orderCount": 1, "processingTime": "2964ms", "success": true}}, {"timestamp": "2025-06-05T15:42:38.546Z", "level": "DEBUG", "module": "UI", "message": "显示订单结果", "data": {"orderCount": 1, "otaType": "chong-dealer", "showManualEdit": false}}, {"timestamp": "2025-06-05T15:42:38.559Z", "level": "SUCCESS", "module": "应用", "message": "订单处理完成", "data": {"orderCount": 1}}, {"timestamp": "2025-06-05T15:42:38.560Z", "level": "DEBUG", "module": "UI", "message": "隐藏加载状态", "data": null}, {"timestamp": "2025-06-05T15:42:51.284Z", "level": "INFO", "module": "订单", "message": "开始创建订单流程", "data": null}, {"timestamp": "2025-06-05T15:42:51.285Z", "level": "INFO", "module": "API", "message": "发起获取后端用户请求", "data": null}, {"timestamp": "2025-06-05T15:42:51.425Z", "level": "SUCCESS", "module": "API响应", "message": "GET https://staging.gomyhire.com.my/api/backend_users - 200 ", "data": {"type": "API_RESPONSE", "method": "GET", "url": "https://staging.gomyhire.com.my/api/backend_users", "status": 200, "timestamp": "2025-06-05T15:42:51.425Z", "responseTime": null, "responseData": {"total_count": 12, "users": [{"id": 1, "name": "Super Admin", "phone": "0162234711", "role": "Super Admin"}, {"id": 105, "name": "<PERSON>", "phone": null, "role": "Super Admin"}, {"id": 22, "name": "Zahidah1", "phone": "0132456789", "role": "Operator"}, {"id": 106, "name": "admin", "phone": null, "role": "Operator"}, {"id": 108, "name": "<PERSON><PERSON><PERSON>", "phone": "0123456789", "role": "Operator"}, {"id": 143, "name": "Kk", "phone": "012", "role": "Operator"}, {"id": 206, "name": "OperatorRinglee", "phone": "0181122334", "role": "Operator"}, {"id": 362, "name": "<PERSON><PERSON>", "phone": null, "role": "Operator"}, {"id": 110, "name": "Sub Admin", "phone": "0162234711", "role": "Sub_Admin"}, {"id": 229, "name": "test", "phone": null, "role": "Sub_Admin"}, {"id": 338, "name": "jcy1", "phone": null, "role": "Sub_Admin"}, {"id": 163, "name": "Kok1", "phone": null, "role": "Sub_Operator"}]}}}, {"timestamp": "2025-06-05T15:42:51.426Z", "level": "DEBUG", "module": "AppState", "message": "用户数据缓存已更新", "data": {"userHash": "c3VwZXJfYWRtaW5AZ29teWhpcmUuY29tX2Fub255bW91cw", "key": "backendUsers", "dataSize": 756}}, {"timestamp": "2025-06-05T15:42:51.427Z", "level": "SUCCESS", "module": "API", "message": "获取后端用户成功", "data": {"userCount": 12, "userNames": ["Super Admin", "<PERSON>", "Zahidah1", "admin", "<PERSON><PERSON><PERSON>"]}}, {"timestamp": "2025-06-05T15:42:51.428Z", "level": "INFO", "module": "DataConsistency", "message": "数据一致性验证完成", "data": {"userHash": "c3VwZXJfYWRtaW5AZ29teWhpcmUuY29tX2Fub255bW91cw", "completeness": true, "freshness": true, "association": true, "isValid": true}}, {"timestamp": "2025-06-05T15:42:51.429Z", "level": "DEBUG", "module": "UI", "message": "显示加载状态", "data": {"message": "正在创建订单..."}}, {"timestamp": "2025-06-05T15:42:51.430Z", "level": "INFO", "module": "订单", "message": "准备创建 1 个订单", "data": null}, {"timestamp": "2025-06-05T15:42:51.431Z", "level": "DEBUG", "module": "订单", "message": "正在处理第 1 个订单", "data": {"id": "ORDER_1749138158522_zi0r93oj6", "customer_name": "王倩", "customer_contact": "+60-XXXXXXXXX", "customer_email": "<EMAIL>", "service_type": "pickup", "pickup_location": "klia", "drop_location": "Hilton Garden Inn Kuala Lumpur Jalan Tuanku Abdul Rahman South", "service_date": "2025-05-30", "service_time": "11:40", "order_datetime": "2025-05-30 11:40", "flight_number": "D7333", "passenger_count": 2, "car_type": "sedan", "ota_price": 69, "driver_fee": 65, "extra_requirement": "⚠️请进入GMH Link Chat查询微信二维码进群，扫码进群 发车子资料照片 登入群跟客人打招呼和说明服务性质 , 接单后1小时内看群里没二维码向GMH客服领 没处理1小时会自动取消订单", "other": "5月30日 接机: D7333 11:40抵达 <PERSON><PERSON> <PERSON> <PERSON><PERSON> Dealer", "created_at": "2025-06-05T15:42:38.523Z", "status": "pending", "car_type_id": 5, "sub_category_id": 7, "incharge_by_backend_user_id": 3, "_smartSelection": {"source": "llm", "timestamp": "2025-06-05T15:42:38.528Z", "results": {"vehicle": {"id": 5, "name": "Compact 5 Seater", "reason": "基于乘客数量 2 的精确匹配 [历史推荐: 基于2个相似成功案例的分析]", "method": "passenger_count_exact"}, "service": {"id": 7, "name": "Pickup", "reason": "接机服务", "method": "simplified_mapping"}, "user": {"id": 3, "name": "Operator", "reason": "基于航班服务的时间敏感性分配操作员", "method": "flight_service"}}, "confidence": {"vehicle": 0.95, "service": 0.9, "user": 0.8}, "fallbacks": {}, "processingTime": 14}}}, {"timestamp": "2025-06-05T15:42:51.432Z", "level": "INFO", "module": "API", "message": "发起创建订单请求", "data": {"hasData": true, "dataKeys": ["id", "customer_name", "customer_contact", "customer_email", "service_type", "pickup_location", "drop_location", "service_date", "service_time", "order_datetime", "flight_number", "passenger_count", "car_type", "ota_price", "driver_fee", "extra_requirement", "other", "created_at", "status", "car_type_id", "sub_category_id", "incharge_by_backend_user_id", "_smartSelection"]}}, {"timestamp": "2025-06-05T15:42:51.433Z", "level": "INFO", "module": "API请求", "message": "POST https://staging.gomyhire.com.my/api/create_order", "data": {"type": "API_REQUEST", "method": "POST", "url": "https://staging.gomyhire.com.my/api/create_order", "timestamp": "2025-06-05T15:42:51.433Z", "headers": {"Content-Type": "application/json"}, "requestData": {"id": "ORDER_1749138158522_zi0r93oj6", "customer_name": "王倩", "customer_contact": "***XXXX", "customer_email": "pr***@gmail.com", "service_type": "pickup", "pickup_location": "klia", "drop_location": "Hilton Garden Inn Kuala Lumpur Jalan Tuanku Abdul Rahman South", "service_date": "2025-05-30", "service_time": "11:40", "order_datetime": "2025-05-30 11:40", "flight_number": "D7333", "passenger_count": 2, "car_type": "sedan", "ota_price": 69, "driver_fee": 65, "extra_requirement": "⚠️请进入GMH Link Chat查询微信二维码进群，扫码进群 发车子资料照片 登入群跟客人打招呼和说明服务性质 , 接单后1小时内看群里没二维码向GMH客服领 没处理1小时会自动取消订单", "other": "5月30日 接机: D7333 11:40抵达 <PERSON><PERSON> <PERSON> <PERSON><PERSON> Dealer", "created_at": "2025-06-05T15:42:38.523Z", "status": "pending", "car_type_id": 5, "sub_category_id": 7, "incharge_by_backend_user_id": 3, "_smartSelection": {"source": "llm", "timestamp": "2025-06-05T15:42:38.528Z", "results": {"vehicle": {"id": 5, "name": "Compact 5 Seater", "reason": "基于乘客数量 2 的精确匹配 [历史推荐: 基于2个相似成功案例的分析]", "method": "passenger_count_exact"}, "service": {"id": 7, "name": "Pickup", "reason": "接机服务", "method": "simplified_mapping"}, "user": {"id": 3, "name": "Operator", "reason": "基于航班服务的时间敏感性分配操作员", "method": "flight_service"}}, "confidence": {"vehicle": 0.95, "service": 0.9, "user": 0.8}, "fallbacks": {}, "processingTime": 14}}}}, {"timestamp": "2025-06-05T15:42:51.533Z", "level": "SUCCESS", "module": "API响应", "message": "POST https://staging.gomyhire.com.my/api/create_order - 200 (101ms)", "data": {"type": "API_RESPONSE", "method": "POST", "url": "https://staging.gomyhire.com.my/api/create_order", "status": 200, "timestamp": "2025-06-05T15:42:51.533Z", "responseTime": 101, "responseData": {"status": false, "message": "Data need to be refined", "data": {"validation_error": {"ota_reference_number": ["The ota reference number field is required."]}, "available_fields_to_fill_in": ["sub_category_id", "ota", "ota_reference_number", "ota_price", "customer_name", "customer_contact", "customer_email", "flight_info", "pickup", "pickup_lat", "pickup_long", "date", "time", "destination", "destination_lat", "destination_long", "car_type_id", "passenger_number", "luggage_number", "driver_fee", "driver_collect", "tour_guide", "baby_chair", "meet_and_greet", "extra_requirement", "incharge_by_backend_user_id"]}}}}, {"timestamp": "2025-06-05T15:42:51.534Z", "level": "SUCCESS", "module": "API", "message": "创建订单成功", "data": {"responseTime": "101ms", "orderId": "unknown"}}, {"timestamp": "2025-06-05T15:42:51.536Z", "level": "SUCCESS", "module": "订单", "message": "第 1 个订单创建成功", "data": {"status": false, "message": "Data need to be refined", "data": {"validation_error": {"ota_reference_number": ["The ota reference number field is required."]}, "available_fields_to_fill_in": ["sub_category_id", "ota", "ota_reference_number", "ota_price", "customer_name", "customer_contact", "customer_email", "flight_info", "pickup", "pickup_lat", "pickup_long", "date", "time", "destination", "destination_lat", "destination_long", "car_type_id", "passenger_number", "luggage_number", "driver_fee", "driver_collect", "tour_guide", "baby_chair", "meet_and_greet", "extra_requirement", "incharge_by_backend_user_id"]}}}, {"timestamp": "2025-06-05T15:42:51.540Z", "level": "DEBUG", "module": "UI", "message": "隐藏加载状态", "data": null}, {"timestamp": "2025-06-05T15:42:51.543Z", "level": "DEBUG", "module": "UI", "message": "显示创建结果", "data": {"successCount": 1, "recoveredCount": 0, "totalCount": 1}}]}